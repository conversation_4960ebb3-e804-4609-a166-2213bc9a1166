<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="32700.99.1234" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22684"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" rowHeight="52" id="KGk-i7-Jjw" customClass="AddToCartTableViewCell" customModule="Dhaiban" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="409" height="52"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="409" height="52"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="JF0-WL-wwg">
                        <rect key="frame" x="159.33333333333337" y="3.6666666666666679" width="233.66666666666663" height="45"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="45" id="tQ7-Z9-HvT"/>
                        </constraints>
                        <state key="normal" title="Button"/>
                        <buttonConfiguration key="configuration" style="plain" title="Button"/>
                        <connections>
                            <action selector="didTappedOnAddToCardButton:" destination="KGk-i7-Jjw" eventType="touchUpInside" id="xEI-C6-awh"/>
                        </connections>
                    </button>
                    <stackView opaque="NO" contentMode="scaleToFill" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="VSn-9T-jMS">
                        <rect key="frame" x="15.999999999999993" y="8.6666666666666643" width="127.33333333333331" height="35"/>
                        <subviews>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="7EF-jj-BK6">
                                <rect key="frame" x="0.0" y="0.0" width="35" height="35"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="35" id="9XA-Pz-OUi"/>
                                </constraints>
                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                <state key="normal" image="minus" catalog="system"/>
                                <buttonConfiguration key="configuration" style="plain" image="minus" catalog="system"/>
                                <connections>
                                    <action selector="didTappedOnMinusButton:" destination="KGk-i7-Jjw" eventType="touchUpInside" id="dhf-tG-dCo"/>
                                </connections>
                            </button>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="sh5-ZW-oc4">
                                <rect key="frame" x="43" y="0.0" width="41.333333333333343" height="35"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Xer-u7-RHI">
                                <rect key="frame" x="92.333333333333329" y="0.0" width="35" height="35"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="35" id="GFl-Kt-RAF"/>
                                    <constraint firstAttribute="height" constant="35" id="N1Z-Bt-33e"/>
                                </constraints>
                                <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                <state key="normal" image="plus" catalog="system"/>
                                <buttonConfiguration key="configuration" style="plain" image="plus" catalog="system" title=""/>
                                <connections>
                                    <action selector="didTappedOnPlusButton:" destination="KGk-i7-Jjw" eventType="touchUpInside" id="Spt-2I-WH3"/>
                                </connections>
                            </button>
                        </subviews>
                    </stackView>
                </subviews>
                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                <constraints>
                    <constraint firstItem="JF0-WL-wwg" firstAttribute="leading" secondItem="VSn-9T-jMS" secondAttribute="trailing" constant="16" id="0PD-QU-N4M"/>
                    <constraint firstAttribute="trailing" secondItem="JF0-WL-wwg" secondAttribute="trailing" constant="16" id="FqT-4p-rkR"/>
                    <constraint firstItem="VSn-9T-jMS" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="KPd-Qd-cMG"/>
                    <constraint firstItem="VSn-9T-jMS" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="16" id="gLC-xs-2ki"/>
                    <constraint firstItem="JF0-WL-wwg" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="m1T-bt-HjO"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="njF-e1-oar"/>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <connections>
                <outlet property="addToCartButtton" destination="JF0-WL-wwg" id="YWj-Qm-nUc"/>
                <outlet property="minusButton" destination="7EF-jj-BK6" id="GB3-gQ-G9G"/>
                <outlet property="plusButton" destination="Xer-u7-RHI" id="kGn-zW-lk4"/>
                <outlet property="productAmountLabel" destination="sh5-ZW-oc4" id="8bW-xf-f7G"/>
            </connections>
            <point key="canvasLocation" x="197.70992366412213" y="-8.4507042253521139"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="minus" catalog="system" width="128" height="26"/>
        <image name="plus" catalog="system" width="128" height="113"/>
    </resources>
</document>
