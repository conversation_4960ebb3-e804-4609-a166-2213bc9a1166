//
//  FilterViewController.swift
//  Dhaiban
//
//  Created by osx on 04/02/2024.
//

import UIKit
import RxCocoa
import RxSwift

struct Filter {
    let title: String
    let key: String
    var options: [FilterOption]
    let isMultipleSelection: Bool
    
    init(title: String, key: String, options: [FilterOption], isMultipleSelection: Bool = false) {
        self.title = title
        self.key = key
        self.options = options
        self.isMultipleSelection = isMultipleSelection
    }
    
    mutating func clear() {
        for i in options.indices {
            options[i].isSelected = false
        }
    }
}

struct FilterOption {
    let id: Int
    let title: String
    var isSelected = false
}

typealias FilterSelections = [String : [Int]]

class FilterViewController: UIViewController, OptionChooseTableViewCellDelegate  {
    
    // MARK: - IBOutlets
    
    @IBOutlet private weak var navigationView: NavigationView!
    @IBOutlet private weak var confirmButton: UIButton!
    @IBOutlet private weak var clearButton: UIButton!
    @IBOutlet private weak var filterTableView: UITableView!
    
    // MARK: - Properties

    var onFiltersUpdated: ((_ selections: FilterSelections) -> Void)?
    
    private var subCategoriesViewModel = SubCategoriesViewModel()
    private var sectionData: [String] = []
    private var rowData: [[String]] = []
    private var selectedSections: Set<Int> = []
    var count = 0
    var maxPriceOption: Int?
    var minPriceOption: Int?
    private let categoryID: Int
    private let disposeBag = DisposeBag()
    private var filters = [
        Filter(
            title: "sorted By",
            key: "sort_by",
            options: [
                FilterOption.init(id: 1, title: "Newly Arrived"),
                FilterOption.init(id: 2, title: "Pre-existing"),
                FilterOption.init(id: 3, title: "Price: Low to High"),
                FilterOption.init(id: 4, title: "Price: High to Low"),
                FilterOption.init(id: 5, title: "Offers and Discounts"),
                FilterOption.init(id: 6, title: "Rating: High to Low")
            ]
        )
    ]
    private var selections: FilterSelections = [:]
    
    // MARK: - Initialization
    
    init(categoryID: Int) {
        self.categoryID = categoryID
        super.init(nibName: nil, bundle: nil)
    }
   
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - View Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupTableView()
        configureNavigationView()
        buttonStyle()
        observeTableViewSelection()
        fetchData()
    }
    
    // MARK: - Setup Methods
    
    private func setupTableView() {
        filterTableView.delegate = self
        filterTableView.dataSource = self
        registerTableViewCells()
    }
    
    private func configureNavigationView() {
        navigationView.configure(title: appLanguageKeys?.filter, showNotification: false, delegate: self)
    }
    
    private func registerTableViewCells() {
        filterTableView.registerNib(cell: ChooseTableViewCell.self)
        filterTableView.registerNib(cell: OptionChooseTableViewCell.self)
        filterTableView.registerNib(cell: PriceTableViewCell.self)
    }
    
    // MARK: - Data Handling
    
    private func fetchData() {
        subCategoriesViewModel.fetchListingProducts(categoryID: categoryID, filter: [:], color: nil, brandID: nil, sortBy: nil, searchQuery: nil, minPrice: nil, maxPrice: nil, page: nil, attribute4: nil, attribute5: nil, sellerID: nil, offerGroupID: nil)
            .subscribe(onNext: { [weak self] filterProductsModel in
                guard let self = self else { return }
                for attribute in filterProductsModel.attributes ?? [] {
                    guard let title = attribute.title, let options = attribute.options else { continue }
                    filters.append(Filter(
                        title: title,
                        key: "attribute_\(attribute.idString)",
                        options: options.compactMap { option in
                            guard let id = option.id, let title = option.title else { return nil }
                            return FilterOption(id: id, title: title)
                        },
                        isMultipleSelection: true
                    ))
                }
                
                
                if let colorOptions = filterProductsModel.allColors, !colorOptions.isEmpty {
                    filters.append(Filter(title: "Colors", key: "color", options: colorOptions.map { FilterOption(id: $0.id, title: $0.colorCode) }))
                }
                
                // price
                if let maxPriceOption = filterProductsModel.originalMaxPrice {
                    self.maxPriceOption = maxPriceOption
                }
                if let  minPriceOption = filterProductsModel.originalMinPrice {
                    self.minPriceOption = minPriceOption
                }
                
                
                DispatchQueue.main.async {
                    self.filterTableView.reloadData()
                }
                
            }, onError: { [weak self] error in
                print("Error fetching products: \(error)")
            })
            .disposed(by: disposeBag)
    }
    
    // MARK: - Button Actions
    
    @IBAction func confirmButtonPressed(_ sender: UIButton) {
        onFiltersUpdated?(selections)
        self.navigationController?.popViewController(animated: true)
        
    }
    
    @IBAction func clearButtonPressed(_ sender: UIButton) {
        clearSelectedOptions()
    }
    
    // MARK: - OptionChooseTableViewCellDelegate
    
    func didSelectOption(filterKey: String, selectedOptions: [Int]) {
        selections[filterKey] = selectedOptions
    }
    
    // MARK: - Private Methods
    
    private func clearSelectedOptions() {
//        selectedSections = []
        filterTableView.reloadData()
    }
    
    private func observeTableViewSelection() {
        filterTableView.rx.itemSelected
            .subscribe(onNext: { [weak self] indexPath in
                guard let self = self else { return }
                self.handleCellSelection(at: indexPath, in: self.filterTableView)
            })
            .disposed(by: disposeBag)
    }
    
    private func handleCellSelection(at indexPath: IndexPath, in tableView: UITableView) {
        guard indexPath.section != filters.count else { return } // continue only if any section except the price range section (last section)
        
        toggleSectionSelection(at: indexPath)
    }
    
//    private func toggleOptionCellVisibility() {
//        if selectedSections.contains(0) {
//            selectedSections.remove(0)
//        } else {
//            selectedSections.insert(0)
//        }
//        filterTableView.reloadSections(IndexSet(integer: 0), with: .automatic)
//    }
    
    private func toggleSectionSelection(at indexPath: IndexPath) {
        if selectedSections.contains(indexPath.section) {
            selectedSections.remove(indexPath.section)
        } else {
            selectedSections.insert(indexPath.section)
        }
        filterTableView.reloadSections(IndexSet(integer: indexPath.section), with: .automatic)
    }
    
//    private func handleOptionChooseCellSelection(at indexPath: IndexPath) {
//        print("Selected Option: \(rowData[indexPath.section - 1][indexPath.row - 1])")
//    }
}

// MARK: - UITableViewDelegate, UITableViewDataSource

extension FilterViewController: UITableViewDelegate, UITableViewDataSource {
    
    func numberOfSections(in tableView: UITableView) -> Int {
        return filters.count + 1
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        switch section {
        case filters.count:
            return 1
        default:
            return 2
        }
      
    }
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        switch indexPath.section {
        case filters.count:
            return configurePriceTableViewCell(for: indexPath, in: tableView)
        default:
            if indexPath.row == 0 {
                return configureFilterSectionTableViewCell(for: indexPath, in: tableView)
            } else {
                return configureFilterOptionsTableViewCell(for: indexPath, in: tableView)
            }
        }
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        switch indexPath.section {
        case filters.count:
            return 100
//        case 0:
//            return selectedSections.contains(0) && indexPath.row == 1 ? 250 : 50
//        case sectionData.count + 1:
//            return 100
        default:
            return selectedSections.contains(indexPath.section) && indexPath.row == 1 ? 0 : UITableView.automaticDimension
        }
    }
    
    // MARK: - Private TableView Cell Configuration
    
    private func configureFilterOptionsTableViewCell(for indexPath: IndexPath, in tableView: UITableView) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "OptionChooseTableViewCell", for: indexPath) as! OptionChooseTableViewCell
        cell.data = filters[indexPath.section]
        cell.delegate = self
        return cell
    }
    
    private func configureFilterSectionTableViewCell(for indexPath: IndexPath, in tableView: UITableView) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "ChooseTableViewCell", for: indexPath) as! ChooseTableViewCell
//        let sectionIndex = indexPath.section - 1
//        if sectionIndex >= 0 && sectionIndex < sectionData.count {
//            cell.configureCell(withTitle: sectionData[sectionIndex])
//        }
        cell.configureCell(withTitle: filters[indexPath.section].title)
        return cell
    }
    
    private func configureFixedTableViewCell(title: String, for indexPath: IndexPath, in tableView: UITableView) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "ChooseTableViewCell", for: indexPath) as! ChooseTableViewCell
        cell.sectionLabel.text = title
        cell.optionButton.setImage(UIImage(systemName: selectedSections.contains(0) ? "chevron.up" : "chevron.down"), for: .normal)
        return cell
    }
    
    private func configurePriceTableViewCell(for indexPath: IndexPath, in tableView: UITableView) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "PriceTableViewCell", for: indexPath) as! PriceTableViewCell
        if let min = minPriceOption, let max = maxPriceOption {
            cell.update(min: min, max: max)
        }
        cell.delegate = self
        return cell
    }
}

// MARK: - NavigationViewDelegate

extension FilterViewController: NavigationViewDelegate {
    func backViewTapped() {
        navigationController?.popViewController(animated: true)
    }
}

// MARK: - Additional Extensions
extension FilterViewController {
    func buttonStyle() {
        confirmButton.applyStyle(.confirm)
        confirmButton.setTitle("Confirm", for: .normal)
        clearButton.setTitle("Clear", for: .normal)
        clearButton.applyStyle(.clear)
    }
}





