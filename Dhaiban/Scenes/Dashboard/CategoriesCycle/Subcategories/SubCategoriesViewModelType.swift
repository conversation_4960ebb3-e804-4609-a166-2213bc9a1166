//
//  SubCategoriesViewModelType.swift
//  Dhaiban
//
//  Created by osx on 29/01/2024.
//  Refactored for better separation of concerns and cleaner interface
//

import Foundation
import RxSwift
import RxCocoa

// MARK: - ViewModel Type Definition

typealias SubCategoriesViewModelType = SubCategoriesViewModelInputs & SubCategoriesViewModelOutputs

// MARK: - Input Protocol

protocol SubCategoriesViewModelInputs {

    /// Fetch subcategories for a given category
    /// - Parameter categoryID: The ID of the parent category
    func fetchSubCategories(categoryID: Int)

    /// Fetch category slider/banner data
    /// - Parameter categoryID: The ID of the category
    func fetchCatergorySlider(categoryID: Int)

    /// Fetch products with comprehensive filtering options
    /// - Parameters:
    ///   - categoryID: The category ID to fetch products from
    ///   - filter: Filter selections applied by user
    ///   - color: Color filter ID
    ///   - brandID: Brand filter ID
    ///   - sortBy: Sort option ID
    ///   - searchQuery: Search text
    ///   - minPrice: Minimum price filter
    ///   - maxPrice: Maximum price filter
    ///   - page: Page number for pagination
    ///   - attribute4: Custom attribute filter
    ///   - attribute5: Custom attribute filter
    ///   - sellerID: Seller filter ID
    ///   - offerGroupID: Offer group filter ID
    /// - Returns: Observable stream of filtered products model
    func fetchListingProducts(
        categoryID: Int,
        filter: FilterSelections,
        color: Int?,
        brandID: Int?,
        sortBy: Int?,
        searchQuery: String?,
        minPrice: Int?,
        maxPrice: Int?,
        page: Int?,
        attribute4: Int?,
        attribute5: Int?,
        sellerID: Int?,
        offerGroupID: Int?
    ) -> Observable<FilterProductsModel>

    /// Update categories with new filter results
    /// - Parameter items: The filtered products model to update with
    func updateCategories(_ items: FilterProductsModel)

    /// Reset pagination state
    func resetPagination()
}

// MARK: - Output Protocol

protocol SubCategoriesViewModelOutputs {

    // MARK: - Observables

    /// Observable stream of error messages
    var errorMessageObservable: Observable<AppErrorType> { get }

    /// Observable stream of subcategories
    var subCategoriesObservable: Observable<[SubcategoriesModelItem]> { get }

    /// Observable stream of category products
    var categoryProductsObservable: Observable<[ProductModelItem]> { get }

    /// Observable stream of subcategories count
    var subCategoriesCountObservable: Observable<Int> { get }

    // MARK: - Behavior Relays

    /// Home slider/banner data
    var homeSlider: BehaviorRelay<[BannerDataModel]> { get }

    /// List of products
    var listOfProducts: BehaviorRelay<[ProductModelItem]> { get }

    // MARK: - State Properties

    /// Current count of subcategories
    var subCategoriesCount: Int { get }

    /// Current count of listing products
    var listingProductCount: Int { get }

    /// Current page number for pagination
    var currentPage: Int { get }

    /// Whether a fetch operation is currently in progress
    var isFetching: Bool { get }

    /// URL for the next page of results
    var nextPageUrl: String? { get }

    /// Total number of pages available
    var totalPages: Int { get }

    /// Current listing products array
    var listingProduct: [ProductModelItem] { get }
}
