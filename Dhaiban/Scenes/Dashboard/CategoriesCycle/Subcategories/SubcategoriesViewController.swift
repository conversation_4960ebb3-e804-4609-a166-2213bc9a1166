//
//  SubcategoriesViewController.swift
//  Dhaiban
//
//  Created by osx on 28/01/2024.
//  Refactored for better architecture and maintainability
//

import UIKit
import RxSwift
import RxCocoa

// MARK: - Section Types

private enum SubcategoriesSection: Int, CaseIterable {
    case search = 0
    case subcategories = 1
    case banner = 2
    case products = 3

    var identifier: String {
        switch self {
        case .search: return "SearchSection"
        case .subcategories: return "SubcategoriesSection"
        case .banner: return "BannerSection"
        case .products: return "ProductsSection"
        }
    }
}

// MARK: - Main View Controller

class SubcategoriesViewController: BaseScreen, DashboardCoordinating, NavigationViewDelegate {

    // MARK: - IBOutlets

    @IBOutlet private weak var subCategoriesTableView: UITableView!
    @IBOutlet private weak var navigationView: NavigationView!

    // MARK: - Properties

    weak var coordinator: DashboardCoordinatorProtocol?

    private let categoryID: Int
    private let categoryTitle: String
    private let viewModel: SubCategoriesViewModelType

    // MARK: - State Management

    private struct ViewState {
        var isLoading: Bool = false
        var isLoadingMore: Bool = false
        var currentFilter: FilterSelections = [:]
        var productsCellHeight: CGFloat = 0
        var hasError: Bool = false
        var errorMessage: String = ""
    }

    private var viewState = ViewState() {
        didSet {
            updateUI()
        }
    }

    // MARK: - Initialization

    init(viewModel: SubCategoriesViewModelType, categoryID: Int, categoryTitle: String) {
        self.viewModel = viewModel
        self.categoryID = categoryID
        self.categoryTitle = categoryTitle
        super.init()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - View Lifecycle

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        bindViewModel()
        loadInitialData()
    }
}

// MARK: - Setup Methods

private extension SubcategoriesViewController {

    func setupUI() {
        configureTableView()
        configureNavigationView()
    }

    func configureTableView() {
        subCategoriesTableView.registerNib(cell: SearchTableViewCell.self)
        subCategoriesTableView.registerNib(cell: SectionTableViewCell.self)
        subCategoriesTableView.registerNib(cell: BannerTableViewCell.self)
        subCategoriesTableView.registerNib(cell: CategoryproductTableViewCell.self)

        subCategoriesTableView.delegate = self
        subCategoriesTableView.dataSource = self
        subCategoriesTableView.separatorStyle = .none
        subCategoriesTableView.showsVerticalScrollIndicator = false
        subCategoriesTableView.backgroundColor = .clear

        // Add refresh control
        let refreshControl = UIRefreshControl()
        refreshControl.addTarget(self, action: #selector(refreshData), for: .valueChanged)
        subCategoriesTableView.refreshControl = refreshControl
    }

    func configureNavigationView() {
        navigationView.configure(
            title: categoryTitle,
            showNotification: false,
            delegate: self
        )

        if let cartImage = UIImage(named: "cartIcon-cart") {
            navigationView.setNotificationImage(cartImage)
        }
    }

    func updateUI() {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            // Update loading state
            if self.viewState.isLoading {
                self.isLoadingRelay.accept(true)
            } else {
                self.isLoadingRelay.accept(false)
                self.subCategoriesTableView.refreshControl?.endRefreshing()
            }

            // Update error state
            if self.viewState.hasError {
                self.onErrorMessageRelay.accept(self.viewState.errorMessage)
            }

            // Update table view
            self.subCategoriesTableView.reloadData()
        }
    }

    @objc func refreshData() {
        loadInitialData()
    }
}

// MARK: - Data Loading

private extension SubcategoriesViewController {

    func loadInitialData() {
        viewState.isLoading = true
        viewState.currentFilter = [:]

        // Load subcategories and initial products
        viewModel.fetchSubCategories(categoryID: categoryID)
        fetchCategoryProducts(filter: viewState.currentFilter, isRefresh: true)
    }

    func fetchCategoryProducts(filter: FilterSelections, isRefresh: Bool = false) {
        if isRefresh {
            viewState.isLoading = true
        } else {
            viewState.isLoadingMore = true
        }

        let page = isRefresh ? 1 : viewModel.currentPage

        viewModel.fetchListingProducts(
            categoryID: categoryID,
            filter: filter,
            color: nil,
            brandID: nil,
            sortBy: nil,
            searchQuery: nil,
            minPrice: nil,
            maxPrice: nil,
            page: page,
            attribute4: nil,
            attribute5: nil,
            sellerID: nil,
            offerGroupID: nil
        )
        .subscribe(
            onNext: { [weak self] filterProductsModel in
                self?.handleProductsResponse(filterProductsModel, isRefresh: isRefresh)
            },
            onError: { [weak self] error in
                self?.handleError(error)
            }
        )
        .disposed(by: disposeBag)
    }

    func handleProductsResponse(_ filterProductsModel: FilterProductsModel, isRefresh: Bool) {
        let productCount = filterProductsModel.products.count

        // Calculate cell height based on product count
        viewState.productsCellHeight = calculateProductsCellHeight(for: productCount)

        // Update loading states
        viewState.isLoading = false
        viewState.isLoadingMore = false
        viewState.hasError = false

        // Check for empty data
        subCategoriesTableView.checkIfNoDataFounded(
            countOfData: productCount,
            type: .noProduct
        )
    }

    func handleError(_ error: Error) {
        viewState.isLoading = false
        viewState.isLoadingMore = false
        viewState.hasError = true
        viewState.errorMessage = error.localizedDescription

        print("Error fetching products: \(error)")
    }

    func calculateProductsCellHeight(for productCount: Int) -> CGFloat {
        guard productCount > 0 else { return 0 }
        return productCount == 1 ? 280 : CGFloat(productCount) * 180
    }
}

// MARK: - ViewModel Binding

private extension SubcategoriesViewController {

    func bindViewModel() {
        bindSubcategories()
        bindProducts()
        bindBanners()
        bindErrors()
    }

    func bindSubcategories() {
        viewModel.subCategoriesObservable
            .observe(on: MainScheduler.instance)
            .subscribe(onNext: { [weak self] _ in
                self?.reloadSection(.subcategories)
            })
            .disposed(by: disposeBag)
    }

    func bindProducts() {
        viewModel.categoryProductsObservable
            .observe(on: MainScheduler.instance)
            .subscribe(onNext: { [weak self] products in
                self?.viewState.productsCellHeight = self?.calculateProductsCellHeight(for: products.count) ?? 0
                self?.reloadSection(.products)
            })
            .disposed(by: disposeBag)
    }

    func bindBanners() {
        viewModel.homeSlider
            .observe(on: MainScheduler.instance)
            .subscribe(onNext: { [weak self] _ in
                self?.reloadSection(.banner)
            })
            .disposed(by: disposeBag)
    }

    func bindErrors() {
        viewModel.errorMessageObservable
            .observe(on: MainScheduler.instance)
            .subscribe(onNext: { [weak self] error in
                self?.handleError(error)
            })
            .disposed(by: disposeBag)
    }

    func reloadSection(_ section: SubcategoriesSection) {
        let indexSet = IndexSet(integer: section.rawValue)
        subCategoriesTableView.reloadSections(indexSet, with: .none)
    }
}

// MARK: - Table View Data Source

extension SubcategoriesViewController: UITableViewDataSource {

    func numberOfSections(in tableView: UITableView) -> Int {
        return SubcategoriesSection.allCases.count
    }

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return 1
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let sectionType = SubcategoriesSection(rawValue: indexPath.section) else {
            return UITableViewCell()
        }

        switch sectionType {
        case .search:
            return configureSearchCell()
        case .subcategories:
            return configureSubcategoriesCell()
        case .banner:
            return configureBannerCell()
        case .products:
            return configureProductsCell()
        }
    }
}

// MARK: - Cell Configuration

private extension SubcategoriesViewController {

    func configureSearchCell() -> UITableViewCell {
        let cell = subCategoriesTableView.dequeue() as SearchTableViewCell
        cell.setFilterIcon()
        cell.setReadOnly()

        cell.onShowFilters = { [weak self] in
            self?.showFilterViewController()
        }

        cell.onShowNotifications = { [weak self] in
            self?.notificationsButtonTapped()
        }

        cell.onTap = { [weak self] in
            self?.coordinator?.showSearchViewController()
        }

        return cell
    }

    func configureSubcategoriesCell() -> UITableViewCell {
        let cell = subCategoriesTableView.dequeue() as SectionTableViewCell
        let placeholderItem = SubcategoriesModelItem(
            id: -1,
            title: "View All",
            logoURL: "viewAll",
            categoryID: categoryID,
            subcategories: nil
        )

        viewModel.subCategoriesObservable
            .subscribe(onNext: { [weak self] subcategories in
                cell.update(subcategories, placeholderItem: placeholderItem)
            })
            .disposed(by: disposeBag)

        cell.onSelected { [weak self] id in
            self?.handleSubcategorySelection(id: id)
        }

        return cell
    }

    func configureBannerCell() -> UITableViewCell {
        let cell = subCategoriesTableView.dequeue() as BannerTableViewCell
        cell.delegate = self

        // Fetch slider data if not already loaded
        viewModel.fetchCatergorySlider(categoryID: categoryID)

        viewModel.homeSlider
            .subscribe(onNext: { items in
                cell.update(items)
            })
            .disposed(by: disposeBag)

        return cell
    }

    func configureProductsCell() -> UITableViewCell {
        let cell = subCategoriesTableView.dequeue() as CategoryproductTableViewCell
        cell.delegate = self

        viewModel.categoryProductsObservable
            .subscribe(onNext: { items in
                cell.update(items)
            })
            .disposed(by: disposeBag)

        return cell
    }
}

// MARK: - User Actions

private extension SubcategoriesViewController {

    func showFilterViewController() {
        coordinator?.showFilterViewController(
            categoryId: categoryID,
            onFiltersUpdated: { [weak self] selections in
                self?.applyFilters(selections)
            }
        )
    }

    func applyFilters(_ selections: FilterSelections) {
        viewState.currentFilter = selections
        fetchCategoryProducts(filter: selections, isRefresh: true)
    }

    func handleSubcategorySelection(id: Int) {
        let targetCategoryID = id == -1 ? categoryID : id

        viewModel.fetchListingProducts(
            categoryID: targetCategoryID,
            filter: [:],
            color: nil,
            brandID: nil,
            sortBy: nil,
            searchQuery: nil,
            minPrice: nil,
            maxPrice: nil,
            page: 1,
            attribute4: nil,
            attribute5: nil,
            sellerID: nil,
            offerGroupID: nil
        )
        .subscribe(
            onNext: { [weak self] filterProductsModel in
                self?.handleSubcategoryProductsResponse(filterProductsModel)
            },
            onError: { [weak self] error in
                self?.handleError(error)
            }
        )
        .disposed(by: disposeBag)
    }

    func handleSubcategoryProductsResponse(_ filterProductsModel: FilterProductsModel) {
        let productCount = filterProductsModel.products.count
        viewState.productsCellHeight = calculateProductsCellHeight(for: productCount)

        viewModel.updateCategories(filterProductsModel)

        // Check for empty data
        subCategoriesTableView.checkIfNoDataFounded(
            countOfData: productCount,
            type: .noProduct
        )

        reloadSection(.products)
    }
}

// MARK: - Table View Delegate

extension SubcategoriesViewController: UITableViewDelegate {

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        guard let sectionType = SubcategoriesSection(rawValue: indexPath.section) else {
            return 0
        }

        switch sectionType {
        case .search:
            return 70
        case .subcategories:
            return viewModel.subCategoriesCount == 0 ? 0 : 132
        case .banner:
            return viewModel.homeSlider.value.isEmpty ? 0 : 197
        case .products:
            return viewState.productsCellHeight
        }
    }

    func scrollViewDidScroll(_ scrollView: UIScrollView) {
        handlePaginationScroll(scrollView)
    }
}

// MARK: - Pagination

private extension SubcategoriesViewController {

    func handlePaginationScroll(_ scrollView: UIScrollView) {
        let offsetY = scrollView.contentOffset.y
        let contentHeight = scrollView.contentSize.height
        let height = scrollView.frame.size.height

        // Check if user scrolled near the bottom (with some buffer)
        let threshold: CGFloat = 100
        if offsetY > contentHeight - height - threshold {
            loadNextPageIfAvailable()
        }
    }

    func loadNextPageIfAvailable() {
        guard !viewState.isLoadingMore,
              !viewState.isLoading,
              let nextPageUrl = viewModel.nextPageUrl,
              let nextPage = extractPageNumber(from: nextPageUrl) else {
            return
        }

        fetchNextPage(nextPage)
    }

    func fetchNextPage(_ page: Int) {
        viewState.isLoadingMore = true

        viewModel.fetchListingProducts(
            categoryID: categoryID,
            filter: viewState.currentFilter,
            color: nil,
            brandID: nil,
            sortBy: nil,
            searchQuery: nil,
            minPrice: nil,
            maxPrice: nil,
            page: page,
            attribute4: nil,
            attribute5: nil,
            sellerID: nil,
            offerGroupID: nil
        )
        .subscribe(
            onNext: { [weak self] filterProductsModel in
                self?.handleNextPageResponse(filterProductsModel)
            },
            onError: { [weak self] error in
                self?.handleError(error)
            }
        )
        .disposed(by: disposeBag)
    }

    func handleNextPageResponse(_ filterProductsModel: FilterProductsModel) {
        viewState.isLoadingMore = false

        let productCount = filterProductsModel.products.count

        // Update cell height for all products (existing + new)
        let totalProducts = viewModel.listingProduct.count + productCount
        viewState.productsCellHeight = calculateProductsCellHeight(for: totalProducts)

        // Update the view model with new data
        viewModel.updateCategories(filterProductsModel)

        // Check for empty data
        subCategoriesTableView.checkIfNoDataFounded(
            countOfData: totalProducts,
            type: .noProduct
        )
    }

    func extractPageNumber(from urlString: String) -> Int? {
        guard let url = URL(string: urlString),
              let components = URLComponents(url: url, resolvingAgainstBaseURL: false),
              let queryItems = components.queryItems,
              let pageItem = queryItems.first(where: { $0.name == "page" }),
              let pageValue = pageItem.value,
              let pageNumber = Int(pageValue) else {
            return nil
        }
        return pageNumber
    }
}

// MARK: - Navigation Delegate

extension SubcategoriesViewController {

    func backViewTapped() {
        navigationController?.popViewController(animated: true)
    }
}

// MARK: - Product Selection Protocol

extension SubcategoriesViewController: AddToProductToCartProtocol {

    func onSelectedItem(id: Int, title: String) {
        coordinator?.showProductDetails(productId: id, productTitle: title)
    }
}
