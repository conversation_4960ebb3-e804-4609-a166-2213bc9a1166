//
//  SubcategoriesViewControllerTests.swift
//  Dhaiban
//
//  Created by AI Assistant on 24/08/2025.
//  Unit tests for the refactored SubcategoriesViewController
//

import XCTest
import RxSwift
import RxTest
@testable import Dhaiban

class SubcategoriesViewControllerTests: XCTestCase {
    
    var sut: SubcategoriesViewController!
    var mockViewModel: MockSubCategoriesViewModel!
    var scheduler: TestScheduler!
    var disposeBag: DisposeBag!
    
    override func setUp() {
        super.setUp()
        scheduler = TestScheduler(initialClock: 0)
        disposeBag = DisposeBag()
        mockViewModel = MockSubCategoriesViewModel(scheduler: scheduler)
        
        sut = SubcategoriesViewController(
            viewModel: mockViewModel,
            categoryID: 1,
            categoryTitle: "Test Category"
        )
        
        // Load the view
        sut.loadViewIfNeeded()
    }
    
    override func tearDown() {
        sut = nil
        mockViewModel = nil
        scheduler = nil
        disposeBag = nil
        super.tearDown()
    }
    
    // MARK: - Initialization Tests
    
    func testInitialization() {
        XCTAssertNotNil(sut)
        XCTAssertEqual(sut.categoryID, 1)
        XCTAssertEqual(sut.categoryTitle, "Test Category")
    }
    
    // MARK: - View Loading Tests
    
    func testViewDidLoad_CallsViewModelMethods() {
        // Given
        let expectation = XCTestExpectation(description: "ViewModel methods called")
        
        // When
        sut.viewDidLoad()
        
        // Then
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            XCTAssertTrue(self.mockViewModel.fetchSubCategoriesCalled)
            XCTAssertTrue(self.mockViewModel.fetchListingProductsCalled)
            expectation.fulfill()
        }
        
        wait(for: [expectation], timeout: 1.0)
    }
    
    // MARK: - Table View Tests
    
    func testTableViewSections() {
        // Given
        sut.viewDidLoad()
        
        // When
        let numberOfSections = sut.subCategoriesTableView.numberOfSections
        
        // Then
        XCTAssertEqual(numberOfSections, 4) // search, subcategories, banner, products
    }
    
    func testTableViewRowsInSection() {
        // Given
        sut.viewDidLoad()
        
        // When & Then
        for section in 0..<4 {
            let numberOfRows = sut.subCategoriesTableView.numberOfRows(inSection: section)
            XCTAssertEqual(numberOfRows, 1)
        }
    }
    
    // MARK: - Pagination Tests
    
    func testPaginationTriggersOnScroll() {
        // Given
        sut.viewDidLoad()
        mockViewModel.nextPageUrl = "https://example.com/api/products?page=2"
        
        // When
        let scrollView = sut.subCategoriesTableView!
        scrollView.contentSize = CGSize(width: 100, height: 1000)
        scrollView.frame = CGRect(x: 0, y: 0, width: 100, height: 200)
        scrollView.contentOffset = CGPoint(x: 0, y: 900) // Near bottom
        
        sut.scrollViewDidScroll(scrollView)
        
        // Then
        // This would trigger pagination in real scenario
        XCTAssertNotNil(mockViewModel.nextPageUrl)
    }
}

// MARK: - Mock ViewModel

class MockSubCategoriesViewModel: SubCategoriesViewModelType {
    
    private let scheduler: TestScheduler
    
    // MARK: - Call Tracking
    
    var fetchSubCategoriesCalled = false
    var fetchListingProductsCalled = false
    var fetchCategorySliderCalled = false
    var updateCategoriesCalled = false
    var resetPaginationCalled = false
    
    // MARK: - Properties
    
    var nextPageUrl: String?
    var totalPages: Int = 1
    var listingProduct: [ProductModelItem] = []
    var currentPage: Int = 1
    var isFetching: Bool = false
    var subCategoriesCount: Int = 0
    var listingProductCount: Int = 0
    
    // MARK: - Observables
    
    lazy var errorMessageObservable: Observable<AppErrorType> = {
        scheduler.createHotObservable([]).asObservable()
    }()
    
    lazy var subCategoriesObservable: Observable<[SubcategoriesModelItem]> = {
        scheduler.createHotObservable([
            .next(10, [])
        ]).asObservable()
    }()
    
    lazy var categoryProductsObservable: Observable<[ProductModelItem]> = {
        scheduler.createHotObservable([
            .next(10, [])
        ]).asObservable()
    }()
    
    lazy var subCategoriesCountObservable: Observable<Int> = {
        scheduler.createHotObservable([
            .next(10, 0)
        ]).asObservable()
    }()
    
    lazy var homeSlider = BehaviorRelay<[BannerDataModel]>(value: [])
    lazy var listOfProducts = BehaviorRelay<[ProductModelItem]>(value: [])
    
    // MARK: - Initialization
    
    init(scheduler: TestScheduler) {
        self.scheduler = scheduler
    }
    
    // MARK: - Input Methods
    
    func fetchSubCategories(categoryID: Int) {
        fetchSubCategoriesCalled = true
    }
    
    func fetchCatergorySlider(categoryID: Int) {
        fetchCategorySliderCalled = true
    }
    
    func fetchListingProducts(
        categoryID: Int,
        filter: FilterSelections,
        color: Int?,
        brandID: Int?,
        sortBy: Int?,
        searchQuery: String?,
        minPrice: Int?,
        maxPrice: Int?,
        page: Int?,
        attribute4: Int?,
        attribute5: Int?,
        sellerID: Int?,
        offerGroupID: Int?
    ) -> Observable<FilterProductsModel> {
        fetchListingProductsCalled = true
        
        let mockModel = FilterProductsModel(
            products: [],
            allColors: nil,
            selectedColor: nil,
            query: nil,
            categoryId: nil,
            sellerId: nil,
            brandId: nil,
            sortBy: nil,
            minPrice: nil,
            maxPrice: nil,
            originalMaxPrice: nil,
            originalMinPrice: nil,
            attributes: nil,
            pagination: nil
        )
        
        return Observable.just(mockModel)
    }
    
    func updateCategories(_ items: FilterProductsModel) {
        updateCategoriesCalled = true
    }
    
    func resetPagination() {
        resetPaginationCalled = true
        currentPage = 1
        nextPageUrl = nil
        totalPages = 1
        listingProduct = []
    }
}
