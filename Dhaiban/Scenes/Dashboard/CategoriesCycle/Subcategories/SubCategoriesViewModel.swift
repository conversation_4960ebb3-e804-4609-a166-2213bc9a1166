//
//  SubCategoriesViewModel.swift
//  Dhaiban
//
//  Created by osx on 29/01/2024.
//

import Foundation
import RxSwift
import RxCocoa

extension Subcategory: DomainConvertible {
    func toDomain() -> SubcategoriesModelItem {
        return SubcategoriesModelItem(
            id: id,
            title: title,
            logoURL: logoURL,
            categoryID: categoryID,
            subcategories: subcategories
        )
    }
}
extension CategoryProductsModel: DomainConvertible {
    func toDomain() -> FilterProductsModel {
        return FilterProductsModel(
            products: self.products ?? [],
            allColors: self.allColors,
            selectedColor: self.selectedColor,
            query: self.query,
            categoryId: self.categoryId,
            sellerId: self.sellerId,
            brandId: self.brandId,
            sortBy: self.sortBy,
            minPrice: self.minPrice,
            maxPrice: self.maxPrice,
            originalMaxPrice: self.originalMaxPrice ?? 0,
            originalMinPrice: self.originalMinPrice ?? 0,
            attributes: self.attributes,
            pagination: self.pagination
        )
    }
}



extension Product: DomainConvertible {
    func toDomain() -> ProductModelItem {
        return ProductModelItem(
            id: id,
            title: title,
            country: country,
            shortDesc: shortDesc,
            currentStock: currentStock,
            avgRating: avgRating,
            photo: photo,
            userFavorite: userFavorite,
            productDate: productDate,
            unitPrice: unitPrice,
            discount: discount,
            discountType: discountType,
            measuringUnit: measuringUnit,
            labelText: labelText,
            labelColor: labelColor,
            advancedProduct: advancedProduct,
            isNew: isNew, seller: seller
        )
    }
}

class SubCategoriesViewModel: SubCategoriesViewModelType {
    func fetchSubCategories(categoryID: Int) {
        <#code#>
    }
    
    func fetchCatergorySlider(categoryID: Int) {
        <#code#>
    }
    
    func fetchListingProducts(categoryID: Int, filter: FilterSelections, color: Int?, brandID: Int?, sortBy: Int?, searchQuery: String?, minPrice: Int?, maxPrice: Int?, page: Int?, attribute4: Int?, attribute5: Int?, sellerID: Int?, offerGroupID: Int?) -> RxSwift.Observable<FilterProductsModel> {
        <#code#>
    }
    
    func updateCategories(_ items: FilterProductsModel) {
        <#code#>
    }
    
    func resetPagination() {
        <#code#>
    }
    

    // MARK: - Properties

    // Pagination properties
    var nextPageUrl: String?
    var totalPages: Int = 1
    var listingProduct: [ProductModelItem] = []

    private var _currentPage = 1
    private var _isFetching = false

    var currentPage: Int {
        get { return _currentPage }
        set { _currentPage = newValue }
    }

    var isFetching: Bool { _isFetching }

    // Data relays
    var homeSlider = BehaviorRelay<[BannerDataModel]>(value: [])
    var listOfProducts = BehaviorRelay<[ProductModelItem]>(value: [])

    // Subjects for observables
    private let subCategoriesCountSubject = BehaviorSubject<Int>(value: 0)
    private var errorMessage = PublishSubject<AppErrorType>()
    private var subCategories = PublishSubject<[SubcategoriesModelItem]>()
    private var categoryProducts = PublishSubject<[ProductModelItem]>()
    private var allDataSubject = PublishSubject<FilterProductsModel>()

    // Internal state
    private var currentSubCategories: [SubcategoriesModelItem] = [] {
        didSet {
            subCategoriesCountSubject.onNext(currentSubCategories.count)
        }
    }

    // MARK: - Computed Properties

    var subCategoriesCountObservable: Observable<Int> {
        return subCategoriesCountSubject.asObservable()
    }

    var errorMessageObservable: Observable<AppErrorType> {
        return errorMessage.asObservable()
    }

    var subCategoriesObservable: Observable<[SubcategoriesModelItem]> {
        return subCategories.asObservable()
    }

    var categoryProductsObservable: Observable<[ProductModelItem]> {
        return categoryProducts.asObservable()
    }

    var allDataObservable: Observable<FilterProductsModel> {
        return allDataSubject.asObservable()
    }

    var subCategoriesCount: Int {
        return currentSubCategories.count
    }

    var listingProductCount: Int {
        return listingProduct.count
    }

    // MARK: - Initialization

    init() {
        setupInitialState()
    }

    private func setupInitialState() {
        _currentPage = 1
        _isFetching = false
        nextPageUrl = nil
        totalPages = 1
        listingProduct = []
        currentSubCategories = []
    }
}
    
    // MARK: - API Methods

    func fetchCatergorySlider(categoryID: Int) {
        ServiceLocator.subCategoriesWorker.fetchCatergorySlider(categoryID: categoryID) { [weak self] result in
            guard let self = self else { return }

            switch result {
            case .success(let response):
                let banners = response.data?.slider?.compactMap { $0.toDomain() } ?? []
                self.homeSlider.accept(banners)

            case .failure(let error):
                self.errorMessage.onNext(error)
            }
        }
    }

    func fetchSubCategories(categoryID: Int) {
        ServiceLocator.subCategoriesWorker.fetchSubCategories(categoryID: categoryID) { [weak self] result in
            guard let self = self else { return }

            switch result {
            case .success(let response):
                let subcategoriesItems = response.data?.subcategories?.compactMap { $0.toDomain() } ?? []
                self.currentSubCategories = subcategoriesItems
                self.subCategories.onNext(subcategoriesItems)

            case .failure(let error):
                self.handleError(error)
                self.subCategories.onNext([])
            }
        }
    }
    
    func fetchListingProducts(
        categoryID: Int,
        filter: FilterSelections,
        color: Int?,
        brandID: Int?,
        sortBy: Int?,
        searchQuery: String?,
        minPrice: Int?,
        maxPrice: Int?,
        page: Int?,
        attribute4: Int?,
        attribute5: Int?,
        sellerID: Int?,
        offerGroupID: Int?
    ) -> Observable<FilterProductsModel> {

        return Observable.create { [weak self] observer in
            guard let self = self else {
                observer.onCompleted()
                return Disposables.create()
            }

            // Prevent multiple simultaneous requests
            guard !self._isFetching else {
                observer.onCompleted()
                return Disposables.create()
            }

            self._isFetching = true
            let requestPage = page ?? self._currentPage

            ServiceLocator.subCategoriesWorker.fetchListingProducts(
                categoryID: categoryID,
                filter: filter,
                color: color,
                brandID: brandID,
                searchQuery: searchQuery,
                minPrice: minPrice,
                maxPrice: maxPrice,
                page: requestPage,
                attribute4: attribute4,
                attribute5: attribute5,
                sellerID: sellerID,
                offerGroupID: offerGroupID
            ) { [weak self] result in
                guard let self = self else { return }

                self._isFetching = false

                switch result {
                case .success(let response):
                    self.handleSuccessResponse(response, requestPage: requestPage, observer: observer)

                case .failure(let error):
                    self.handleError(error)
                    observer.onError(error)
                }
            }

            return Disposables.create()
        }
    }

    func updateCategories(_ items: FilterProductsModel) {
        let productItems = items.products.compactMap { $0.toDomain() }

        // Update internal state
        if _currentPage == 1 {
            listingProduct = productItems
        } else {
            listingProduct.append(contentsOf: productItems)
        }

        // Notify observers
        categoryProducts.onNext(listingProduct)
        listOfProducts.accept(listingProduct)
    }
}

    // MARK: - Private Helper Methods

    private func handleSuccessResponse(
        _ response: CategoryProductsResponse,
        requestPage: Int,
        observer: AnyObserver<FilterProductsModel>
    ) {
        guard let data = response.data else {
            if let error = response.errors {
                observer.onError(AppErrorType.responseError(error))
            } else {
                observer.onError(AppErrorType.genericError)
            }
            return
        }

        // Process products
        if let products = data.products {
            let productItems = products.compactMap { $0.toDomain() }

            if requestPage == 1 {
                listingProduct = productItems
            } else {
                listingProduct.append(contentsOf: productItems)
            }

            // Update pagination info
            updatePaginationInfo(from: data.pagination)

            // Notify observers
            categoryProducts.onNext(listingProduct)
            listOfProducts.accept(listingProduct)
        }

        // Create and send domain model
        let filterProductsModel = data.toDomain()
        allDataSubject.onNext(filterProductsModel)
        observer.onNext(filterProductsModel)
        observer.onCompleted()
    }

    private func updatePaginationInfo(from pagination: Pagination?) {
        guard let pagination = pagination else { return }

        nextPageUrl = pagination.nextPageURL
        totalPages = pagination.lastPage ?? 1
        _currentPage = pagination.currentPage ?? 1
    }

    private func handleError(_ error: AppErrorType) {
        errorMessage.onNext(error)
    }

    // MARK: - Reset Methods

    func resetPagination() {
        _currentPage = 1
        nextPageUrl = nil
        totalPages = 1
        listingProduct = []
    }
}

// MARK: - Domain Models

struct SubcategoriesModelItem {
    let id: Int?
    let title: String?
    let logoURL: String?
    let categoryID: Int?
    let subcategories: [Subcategory]?
}

struct ProductModelItem {
    let id: Int?
    let title: String?
    let country: String?
    let shortDesc: String?
    let currentStock: Int?
    let avgRating: Double?
    let photo: String?
    var userFavorite: Bool?
    let productDate: String?
    let unitPrice: Double?
    let discount: Int?
    let discountType: DiscountType?
    let measuringUnit: MeasuringUnit?
    let labelText, labelColor: String?
    let advancedProduct: String?
    let isNew: Bool?
    let seller: String?

    static let example = ProductModelItem(
        id: 0,
        title: "Product 1",
        country: "",
        shortDesc: "",
        currentStock: 10,
        avgRating: 10.0,
        photo: "",
        userFavorite: true,
        productDate: "",
        unitPrice: 20,
        discount: 20,
        discountType: .amount,
        measuringUnit: .kg,
        labelText: "",
        labelColor: "",
        advancedProduct: "",
        isNew: true,
        seller: ""
    )
}

struct FilterProductsModel {
    let products: [Product]
    let allColors: [AllColor]?
    let selectedColor, query: String?
    let categoryId: String?
    let sellerId, brandId, sortBy, minPrice: String?
    let maxPrice: String?
    let originalMaxPrice, originalMinPrice: Int?
    let attributes: [Attribute]?
    let pagination: Pagination?
}

