//
//  DialCodeData.swift
//  D<PERSON>ban
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 02/09/2024.
//

import Foundation

public struct DialCodeData: Decodable {
    public let countryName: String

    public let isoCode: String
    public var flagImage: String { "flag_\(isoCode.lowercased())" }

    public let dialCode: String
    public var displayDialCode: String { "+\(dialCode)" }
}

extension DialCodeData {
    public init(from model: DialCodeModel) {
        self.countryName = model.country
        self.isoCode = model.iso2LetterCode
        self.dialCode = model.phoneCode
    }
}
