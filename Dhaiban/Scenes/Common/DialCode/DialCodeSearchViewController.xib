<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="32700.99.1234" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22685"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="DialCodeSearchViewController" customModule="Dhaiban" customModuleProvider="target">
            <connections>
                <outlet property="searchView" destination="JSb-GS-T9q" id="6Ul-sO-XcX"/>
                <outlet property="tableView" destination="fb8-Rm-xrK" id="JjN-DH-WBR"/>
                <outlet property="titleLabel" destination="e0n-db-FSI" id="c0Q-wr-mul"/>
                <outlet property="view" destination="Kzx-Lj-nCS" id="2IX-22-fzH"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="Kzx-Lj-nCS">
            <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <stackView opaque="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="w9s-DQ-Xdo">
                    <rect key="frame" x="16" y="75" width="361" height="20.333333333333329"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="1000" verticalCompressionResistancePriority="1000" text="Select DialCode" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="e0n-db-FSI" customClass="SheetScreenTitleLabel" customModule="SharedUIModule">
                            <rect key="frame" x="0.0" y="0.0" width="361" height="20.333333333333332"/>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                    </subviews>
                </stackView>
                <view contentMode="scaleToFill" placeholderIntrinsicWidth="368" placeholderIntrinsicHeight="40" translatesAutoresizingMaskIntoConstraints="NO" id="JSb-GS-T9q" customClass="SearchFieldView" customModule="Dhaiban" customModuleProvider="target">
                    <rect key="frame" x="12.666666666666657" y="105.33333333333333" width="368" height="39.999999999999986"/>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                </view>
                <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" style="plain" separatorStyle="default" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="-1" estimatedSectionHeaderHeight="-1" sectionFooterHeight="-1" estimatedSectionFooterHeight="-1" translatesAutoresizingMaskIntoConstraints="NO" id="fb8-Rm-xrK">
                    <rect key="frame" x="0.0" y="145.33333333333331" width="393" height="672.66666666666674"/>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                </tableView>
            </subviews>
            <viewLayoutGuide key="safeArea" id="HdW-4f-Jhz"/>
            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
            <constraints>
                <constraint firstItem="fb8-Rm-xrK" firstAttribute="leading" secondItem="HdW-4f-Jhz" secondAttribute="leading" id="BvF-u3-Tbu"/>
                <constraint firstItem="HdW-4f-Jhz" firstAttribute="trailing" secondItem="JSb-GS-T9q" secondAttribute="trailing" constant="12.5" id="D7h-EI-cTA"/>
                <constraint firstItem="w9s-DQ-Xdo" firstAttribute="top" secondItem="HdW-4f-Jhz" secondAttribute="top" constant="16" id="IFA-8N-E3M"/>
                <constraint firstItem="JSb-GS-T9q" firstAttribute="top" secondItem="w9s-DQ-Xdo" secondAttribute="bottom" constant="10" id="P1E-hn-RuH"/>
                <constraint firstItem="HdW-4f-Jhz" firstAttribute="trailing" secondItem="fb8-Rm-xrK" secondAttribute="trailing" id="RwL-Wo-FbT"/>
                <constraint firstItem="w9s-DQ-Xdo" firstAttribute="leading" secondItem="HdW-4f-Jhz" secondAttribute="leading" constant="16" id="ijU-WN-KmM"/>
                <constraint firstItem="JSb-GS-T9q" firstAttribute="leading" secondItem="HdW-4f-Jhz" secondAttribute="leading" constant="12.5" id="pWL-rB-v8f"/>
                <constraint firstItem="fb8-Rm-xrK" firstAttribute="top" secondItem="JSb-GS-T9q" secondAttribute="bottom" id="rzC-vR-H01"/>
                <constraint firstItem="HdW-4f-Jhz" firstAttribute="trailing" secondItem="w9s-DQ-Xdo" secondAttribute="trailing" constant="16" id="wDI-7m-yh0"/>
                <constraint firstItem="HdW-4f-Jhz" firstAttribute="bottom" secondItem="fb8-Rm-xrK" secondAttribute="bottom" id="wfZ-bO-Kyk"/>
            </constraints>
            <point key="canvasLocation" x="-45" y="-11"/>
        </view>
    </objects>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
