<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21507" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21505"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="LoginBottomSheet" customModule="Dhaiban" customModuleProvider="target">
            <connections>
                <outlet property="cancelButton" destination="IRC-5D-GLv" id="qRc-7V-BDA"/>
                <outlet property="contentView" destination="6nr-Jt-2xo" id="qEy-mk-RX0"/>
                <outlet property="loginButton" destination="Xex-9p-IxO" id="4yz-NI-GJs"/>
                <outlet property="loginImageView" destination="iKm-qL-Gce" id="BQP-pJ-auL"/>
                <outlet property="titleLabel" destination="T8u-gL-GJq" id="NNg-X7-Nmd"/>
                <outlet property="view" destination="6w5-kZ-oZm" id="8SD-cV-Clp"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="6w5-kZ-oZm">
            <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="6nr-Jt-2xo">
                    <rect key="frame" x="0.0" y="452" width="393" height="400"/>
                    <subviews>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="iKm-qL-Gce">
                            <rect key="frame" x="16" y="16" width="361" height="168"/>
                        </imageView>
                        <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="center" spacing="24" translatesAutoresizingMaskIntoConstraints="NO" id="gL9-6a-hzy">
                            <rect key="frame" x="16" y="200" width="361" height="174.33333333333337"/>
                            <subviews>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="wordWrap" baselineAdjustment="alignBaselines" minimumScaleFactor="0.5" translatesAutoresizingMaskIntoConstraints="NO" id="T8u-gL-GJq">
                                    <rect key="frame" x="0.0" y="0.0" width="361" height="20.333333333333332"/>
                                    <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                    <nil key="textColor"/>
                                    <nil key="highlightedColor"/>
                                </label>
                                <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" spacing="20" translatesAutoresizingMaskIntoConstraints="NO" id="HaE-DG-rto">
                                    <rect key="frame" x="0.0" y="44.333333333333371" width="361" height="130"/>
                                    <subviews>
                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Xex-9p-IxO">
                                            <rect key="frame" x="0.0" y="0.0" width="361" height="55"/>
                                            <constraints>
                                                <constraint firstAttribute="height" constant="55" id="PzG-9y-a8h"/>
                                            </constraints>
                                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                            <state key="normal" title="Button"/>
                                            <connections>
                                                <action selector="didTappedOnConfirmButton:" destination="-1" eventType="touchUpInside" id="aaM-lM-87o"/>
                                            </connections>
                                        </button>
                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="IRC-5D-GLv">
                                            <rect key="frame" x="0.0" y="75" width="361" height="55"/>
                                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                                            <state key="normal" title="Button"/>
                                            <connections>
                                                <action selector="didTappedOnCancelButton:" destination="-1" eventType="touchUpInside" id="cqL-SW-5bI"/>
                                            </connections>
                                        </button>
                                    </subviews>
                                </stackView>
                            </subviews>
                            <constraints>
                                <constraint firstItem="HaE-DG-rto" firstAttribute="width" secondItem="gL9-6a-hzy" secondAttribute="width" id="15h-y8-CTy"/>
                                <constraint firstItem="T8u-gL-GJq" firstAttribute="width" secondItem="gL9-6a-hzy" secondAttribute="width" id="anG-NM-aBW"/>
                            </constraints>
                        </stackView>
                    </subviews>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="400" id="1J7-7B-R9I"/>
                        <constraint firstItem="iKm-qL-Gce" firstAttribute="leading" secondItem="6nr-Jt-2xo" secondAttribute="leading" constant="16" id="AMW-vq-VUR"/>
                        <constraint firstAttribute="trailing" secondItem="iKm-qL-Gce" secondAttribute="trailing" constant="16" id="Fb0-Qh-ETK"/>
                        <constraint firstItem="gL9-6a-hzy" firstAttribute="top" secondItem="iKm-qL-Gce" secondAttribute="bottom" constant="16" id="e1Q-Ny-vII"/>
                        <constraint firstItem="gL9-6a-hzy" firstAttribute="leading" secondItem="6nr-Jt-2xo" secondAttribute="leading" constant="16" id="faU-4N-UPa"/>
                        <constraint firstAttribute="bottom" secondItem="gL9-6a-hzy" secondAttribute="bottom" constant="25.666666666666629" id="hd0-Cd-9AY"/>
                        <constraint firstAttribute="trailing" secondItem="gL9-6a-hzy" secondAttribute="trailing" constant="16" id="pkV-Tf-Qwy"/>
                        <constraint firstItem="iKm-qL-Gce" firstAttribute="top" secondItem="6nr-Jt-2xo" secondAttribute="top" constant="16" id="umt-hO-WKs"/>
                    </constraints>
                </view>
            </subviews>
            <viewLayoutGuide key="safeArea" id="hkL-pf-XId"/>
            <color key="backgroundColor" white="0.0" alpha="0.25" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="hkL-pf-XId" firstAttribute="trailing" secondItem="6nr-Jt-2xo" secondAttribute="trailing" id="JZn-py-I0w"/>
                <constraint firstAttribute="bottom" secondItem="6nr-Jt-2xo" secondAttribute="bottom" id="Maz-bJ-GDz"/>
                <constraint firstItem="6nr-Jt-2xo" firstAttribute="leading" secondItem="hkL-pf-XId" secondAttribute="leading" id="l8q-1W-eLY"/>
            </constraints>
            <point key="canvasLocation" x="261.83206106870227" y="-132.3943661971831"/>
        </view>
    </objects>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
