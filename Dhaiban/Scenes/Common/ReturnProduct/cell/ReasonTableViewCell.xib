<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21507" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21505"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" rowHeight="30" id="KGk-i7-Jjw" customClass="ReasonTableViewCell" customModule="Dhaiban" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="313" height="30"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="313" height="30"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <stackView opaque="NO" contentMode="scaleToFill" spacing="201" translatesAutoresizingMaskIntoConstraints="NO" id="xOV-uc-MtL">
                        <rect key="frame" x="0.0" y="0.0" width="313" height="20.333333333333332"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="6CV-fJ-Zcl">
                                <rect key="frame" x="0.0" y="0.0" width="256" height="20.333333333333332"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="icon-circle-empty" translatesAutoresizingMaskIntoConstraints="NO" id="aZj-tC-PUT">
                                <rect key="frame" x="272" y="0.0" width="25" height="25"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="25" id="6pe-Ty-wVq"/>
                                    <constraint firstAttribute="height" constant="25" id="adp-Hb-uJv"/>
                                </constraints>
                            </imageView>
                        </subviews>
                        <constraints>
                            <constraint firstItem="aZj-tC-PUT" firstAttribute="leading" secondItem="6CV-fJ-Zcl" secondAttribute="trailing" constant="16" id="0pZ-Q9-1AO"/>
                            <constraint firstAttribute="bottom" secondItem="aZj-tC-PUT" secondAttribute="bottom" constant="16" id="QLB-Gl-Gbl"/>
                            <constraint firstAttribute="trailing" secondItem="aZj-tC-PUT" secondAttribute="trailing" constant="16" id="V0Q-yk-sSD"/>
                        </constraints>
                    </stackView>
                </subviews>
                <constraints>
                    <constraint firstAttribute="bottom" secondItem="xOV-uc-MtL" secondAttribute="bottom" id="118-tt-F8Y"/>
                    <constraint firstAttribute="trailing" secondItem="xOV-uc-MtL" secondAttribute="trailing" id="31b-O1-vqz"/>
                    <constraint firstItem="xOV-uc-MtL" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" id="66G-sI-LUi"/>
                    <constraint firstItem="xOV-uc-MtL" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" id="8SE-px-gak"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="njF-e1-oar"/>
            <connections>
                <outlet property="checkImage" destination="aZj-tC-PUT" id="P8V-Go-isV"/>
                <outlet property="reasonLabel" destination="6CV-fJ-Zcl" id="6Ly-gu-Hm3"/>
            </connections>
            <point key="canvasLocation" x="124.42748091603053" y="-16.197183098591552"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="icon-circle-empty" width="21.333333969116211" height="21.333333969116211"/>
    </resources>
</document>
