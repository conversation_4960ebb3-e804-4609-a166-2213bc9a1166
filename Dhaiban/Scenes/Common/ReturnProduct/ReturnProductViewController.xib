<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="32700.99.1234" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22684"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="ReturnProductViewController" customModule="Dhaiban" customModuleProvider="target">
            <connections>
                <outlet property="commentTextView" destination="fPz-oH-ozr" id="StU-Lx-Owe"/>
                <outlet property="contantView" destination="ea2-66-CPJ" id="Ntn-lT-Bld"/>
                <outlet property="errorLabel" destination="q74-vs-7tC" id="a9a-nV-46G"/>
                <outlet property="reasonsTableView" destination="7hb-co-EAt" id="owi-00-mIu"/>
                <outlet property="refundReasonLabel" destination="6is-DN-sxU" id="z7N-xO-Ksi"/>
                <outlet property="returnProductLabel" destination="pfr-KE-IW4" id="Yft-Ft-Ls0"/>
                <outlet property="sendButton" destination="CCt-T0-Fy5" id="Wyl-Us-Y2h"/>
                <outlet property="view" destination="E6m-4c-jEl" id="pZ6-tu-V0P"/>
                <outlet property="writeCommentLabel" destination="6Gi-7v-Eo8" id="qyH-G8-4Xa"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="E6m-4c-jEl">
            <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ea2-66-CPJ">
                    <rect key="frame" x="0.0" y="312" width="393" height="540"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="wordWrap" baselineAdjustment="alignBaselines" minimumScaleFactor="0.5" translatesAutoresizingMaskIntoConstraints="NO" id="pfr-KE-IW4">
                            <rect key="frame" x="24" y="24" width="345" height="28"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="28" id="OQU-zU-njV"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="6is-DN-sxU">
                            <rect key="frame" x="16" y="68" width="361" height="21"/>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="6Gi-7v-Eo8">
                            <rect key="frame" x="16" y="294" width="361" height="21"/>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <textView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" textAlignment="natural" translatesAutoresizingMaskIntoConstraints="NO" id="fPz-oH-ozr">
                            <rect key="frame" x="16" y="331" width="361" height="70"/>
                            <color key="backgroundColor" name="DEDEDE"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="70" id="Ww3-fp-OvG"/>
                            </constraints>
                            <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                            <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                        </textView>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="CCt-T0-Fy5">
                            <rect key="frame" x="16" y="454" width="361" height="44"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="44" id="CFK-xI-thY"/>
                            </constraints>
                            <state key="normal" title="Button"/>
                            <buttonConfiguration key="configuration" style="plain" title="Button"/>
                            <connections>
                                <action selector="didTappedOnSendButton:" destination="-1" eventType="touchUpInside" id="7LR-Z4-jTc"/>
                            </connections>
                        </button>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="q74-vs-7tC">
                            <rect key="frame" x="24" y="417" width="345" height="21"/>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" style="plain" separatorStyle="default" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="-1" estimatedSectionHeaderHeight="-1" sectionFooterHeight="-1" estimatedSectionFooterHeight="-1" translatesAutoresizingMaskIntoConstraints="NO" id="7hb-co-EAt">
                            <rect key="frame" x="16" y="105" width="361" height="173"/>
                            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        </tableView>
                    </subviews>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    <constraints>
                        <constraint firstItem="pfr-KE-IW4" firstAttribute="leading" secondItem="ea2-66-CPJ" secondAttribute="leading" constant="24" id="10C-Br-PsG"/>
                        <constraint firstItem="fPz-oH-ozr" firstAttribute="top" secondItem="6Gi-7v-Eo8" secondAttribute="bottom" constant="16" id="2ja-E8-PZl"/>
                        <constraint firstAttribute="trailing" secondItem="CCt-T0-Fy5" secondAttribute="trailing" constant="16" id="3cN-zo-dkV"/>
                        <constraint firstItem="pfr-KE-IW4" firstAttribute="top" secondItem="ea2-66-CPJ" secondAttribute="top" constant="24" id="3ln-97-zdq"/>
                        <constraint firstAttribute="trailing" secondItem="7hb-co-EAt" secondAttribute="trailing" constant="16" id="6l6-wC-4Dg"/>
                        <constraint firstAttribute="trailing" secondItem="6is-DN-sxU" secondAttribute="trailing" constant="16" id="7Ei-ZM-cVd"/>
                        <constraint firstItem="6Gi-7v-Eo8" firstAttribute="top" secondItem="7hb-co-EAt" secondAttribute="bottom" constant="16" id="At9-4V-aSq"/>
                        <constraint firstItem="6is-DN-sxU" firstAttribute="top" secondItem="pfr-KE-IW4" secondAttribute="bottom" constant="16" id="Ggw-NY-HZy"/>
                        <constraint firstItem="6is-DN-sxU" firstAttribute="leading" secondItem="ea2-66-CPJ" secondAttribute="leading" constant="16" id="HjP-q8-Oei"/>
                        <constraint firstAttribute="trailing" secondItem="6is-DN-sxU" secondAttribute="trailing" constant="16" id="IPF-vH-ztz"/>
                        <constraint firstItem="CCt-T0-Fy5" firstAttribute="top" secondItem="q74-vs-7tC" secondAttribute="bottom" constant="16" id="Jge-3k-btN"/>
                        <constraint firstAttribute="trailing" secondItem="q74-vs-7tC" secondAttribute="trailing" constant="24" id="NLy-bT-han"/>
                        <constraint firstItem="6Gi-7v-Eo8" firstAttribute="leading" secondItem="ea2-66-CPJ" secondAttribute="leading" constant="16" id="OiB-LW-RV8"/>
                        <constraint firstItem="CCt-T0-Fy5" firstAttribute="leading" secondItem="ea2-66-CPJ" secondAttribute="leading" constant="16" id="Qol-h9-Nt2"/>
                        <constraint firstAttribute="height" constant="540" id="RY3-rx-tpg"/>
                        <constraint firstAttribute="bottom" secondItem="CCt-T0-Fy5" secondAttribute="bottom" constant="42" id="SC2-Qe-aBw"/>
                        <constraint firstItem="fPz-oH-ozr" firstAttribute="leading" secondItem="ea2-66-CPJ" secondAttribute="leading" constant="16" id="SFY-i0-WMh"/>
                        <constraint firstItem="q74-vs-7tC" firstAttribute="top" secondItem="fPz-oH-ozr" secondAttribute="bottom" constant="16" id="VJa-Jm-QAd"/>
                        <constraint firstItem="pfr-KE-IW4" firstAttribute="centerX" secondItem="ea2-66-CPJ" secondAttribute="centerX" id="ZrD-KA-rxo"/>
                        <constraint firstItem="6is-DN-sxU" firstAttribute="leading" secondItem="ea2-66-CPJ" secondAttribute="leading" constant="16" id="d3C-hS-UGx"/>
                        <constraint firstItem="7hb-co-EAt" firstAttribute="leading" secondItem="ea2-66-CPJ" secondAttribute="leading" constant="16" id="hex-4J-6Za"/>
                        <constraint firstAttribute="trailing" secondItem="6Gi-7v-Eo8" secondAttribute="trailing" constant="16" id="ijW-Dv-8Fw"/>
                        <constraint firstItem="q74-vs-7tC" firstAttribute="leading" secondItem="ea2-66-CPJ" secondAttribute="leading" constant="24" id="rzg-Ff-nFP"/>
                        <constraint firstItem="7hb-co-EAt" firstAttribute="top" secondItem="6is-DN-sxU" secondAttribute="bottom" constant="16" id="tWP-uf-N42"/>
                        <constraint firstAttribute="trailing" secondItem="fPz-oH-ozr" secondAttribute="trailing" constant="16" id="wsr-mU-8ci"/>
                    </constraints>
                </view>
            </subviews>
            <viewLayoutGuide key="safeArea" id="mhF-rY-GaN"/>
            <color key="backgroundColor" white="0.0" alpha="0.25" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstAttribute="bottom" secondItem="ea2-66-CPJ" secondAttribute="bottom" id="1Wi-aP-XA0"/>
                <constraint firstItem="ea2-66-CPJ" firstAttribute="leading" secondItem="mhF-rY-GaN" secondAttribute="leading" id="5dt-Qo-SlM"/>
                <constraint firstItem="mhF-rY-GaN" firstAttribute="trailing" secondItem="ea2-66-CPJ" secondAttribute="trailing" id="lpX-Rz-CNH"/>
            </constraints>
            <point key="canvasLocation" x="1148.************" y="-168.3098591549296"/>
        </view>
    </objects>
    <resources>
        <namedColor name="DEDEDE">
            <color red="0.87058823529411766" green="0.87058823529411766" blue="0.87058823529411766" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
