<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21507" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21505"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="ErrorAlertViewController" customModule="Dhaiban" customModuleProvider="target">
            <connections>
                <outlet property="activeButton" destination="QLn-rc-gLV" id="lvG-YS-5v2"/>
                <outlet property="contentView" destination="9am-RT-5YA" id="M0S-bg-VWg"/>
                <outlet property="errorImageView" destination="ZuT-vK-2P1" id="U39-Ke-g1w"/>
                <outlet property="errorView" destination="oPv-dN-vq1" id="1Lh-V6-mAN"/>
                <outlet property="subtitleLabel" destination="Vmj-Pf-q2L" id="JO7-k5-KJn"/>
                <outlet property="titleLabel" destination="0rY-TL-BNc" id="2by-hE-F3d"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="9am-RT-5YA">
                    <rect key="frame" x="39.333333333333343" y="286" width="314.33333333333326" height="280"/>
                    <subviews>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="oPv-dN-vq1">
                            <rect key="frame" x="122.33333333333331" y="12" width="70" height="70"/>
                            <subviews>
                                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="errorIcon-generic" translatesAutoresizingMaskIntoConstraints="NO" id="ZuT-vK-2P1">
                                    <rect key="frame" x="5" y="5" width="60" height="60"/>
                                    <constraints>
                                        <constraint firstAttribute="height" constant="60" id="Lv8-04-2eW"/>
                                        <constraint firstAttribute="width" constant="60" id="YkL-eL-Uxj"/>
                                    </constraints>
                                </imageView>
                            </subviews>
                            <color key="backgroundColor" name="paleGrey"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="70" id="0xL-I5-dZ3"/>
                                <constraint firstAttribute="width" constant="70" id="5Dw-5i-XG7"/>
                                <constraint firstItem="ZuT-vK-2P1" firstAttribute="centerY" secondItem="oPv-dN-vq1" secondAttribute="centerY" id="VDe-c3-JlG"/>
                                <constraint firstItem="ZuT-vK-2P1" firstAttribute="centerX" secondItem="oPv-dN-vq1" secondAttribute="centerX" id="ZDM-BB-sww"/>
                            </constraints>
                        </view>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="title" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="0rY-TL-BNc">
                            <rect key="frame" x="12" y="106" width="290.33333333333331" height="20.333333333333329"/>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="QLn-rc-gLV">
                            <rect key="frame" x="20" y="212" width="274.33333333333331" height="44"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="44" id="rS2-21-oDQ"/>
                            </constraints>
                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                            <state key="normal" title="حاول مره اخري"/>
                            <connections>
                                <action selector="didTappedOnActiveButton:" destination="-1" eventType="touchUpInside" id="1NN-AA-5Ig"/>
                            </connections>
                        </button>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="subtitle" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="3" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Vmj-Pf-q2L">
                            <rect key="frame" x="12" y="142.33333333333331" width="290.33333333333331" height="20.333333333333343"/>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    <constraints>
                        <constraint firstItem="0rY-TL-BNc" firstAttribute="top" secondItem="oPv-dN-vq1" secondAttribute="bottom" constant="24" id="2tT-lX-Obj"/>
                        <constraint firstItem="QLn-rc-gLV" firstAttribute="leading" secondItem="9am-RT-5YA" secondAttribute="leading" constant="20" id="84E-a4-Dfc"/>
                        <constraint firstAttribute="bottom" secondItem="QLn-rc-gLV" secondAttribute="bottom" constant="24" id="Cdo-yg-hG9"/>
                        <constraint firstItem="oPv-dN-vq1" firstAttribute="top" secondItem="9am-RT-5YA" secondAttribute="top" constant="12" id="EzK-fK-vce"/>
                        <constraint firstAttribute="trailing" secondItem="Vmj-Pf-q2L" secondAttribute="trailing" constant="12" id="Hao-EM-IqS"/>
                        <constraint firstAttribute="trailing" secondItem="QLn-rc-gLV" secondAttribute="trailing" constant="20" id="IsL-5Z-VYa"/>
                        <constraint firstAttribute="height" constant="280" id="VKL-in-bPa"/>
                        <constraint firstItem="0rY-TL-BNc" firstAttribute="leading" secondItem="9am-RT-5YA" secondAttribute="leading" constant="12" id="XbB-HJ-mxg"/>
                        <constraint firstItem="QLn-rc-gLV" firstAttribute="centerX" secondItem="9am-RT-5YA" secondAttribute="centerX" id="bfg-ck-ou5"/>
                        <constraint firstItem="Vmj-Pf-q2L" firstAttribute="top" secondItem="0rY-TL-BNc" secondAttribute="bottom" constant="16" id="lL6-xy-AkB"/>
                        <constraint firstItem="Vmj-Pf-q2L" firstAttribute="leading" secondItem="9am-RT-5YA" secondAttribute="leading" constant="12" id="mTx-bq-xf6"/>
                        <constraint firstItem="oPv-dN-vq1" firstAttribute="centerX" secondItem="9am-RT-5YA" secondAttribute="centerX" id="mW6-F6-UtE"/>
                        <constraint firstAttribute="trailing" secondItem="0rY-TL-BNc" secondAttribute="trailing" constant="12" id="rmj-28-CDr"/>
                    </constraints>
                </view>
            </subviews>
            <viewLayoutGuide key="safeArea" id="fnl-2z-Ty3"/>
            <color key="backgroundColor" white="0.0" alpha="0.20000000000000001" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="9am-RT-5YA" firstAttribute="centerY" secondItem="i5M-Pr-FkT" secondAttribute="centerY" id="CeV-2O-g1g"/>
                <constraint firstItem="9am-RT-5YA" firstAttribute="centerX" secondItem="i5M-Pr-FkT" secondAttribute="centerX" id="sQg-sy-ZHZ"/>
                <constraint firstItem="9am-RT-5YA" firstAttribute="width" secondItem="i5M-Pr-FkT" secondAttribute="width" multiplier="0.8" id="trN-7e-Nll"/>
            </constraints>
            <point key="canvasLocation" x="100" y="19.718309859154932"/>
        </view>
    </objects>
    <resources>
        <image name="errorIcon-generic" width="64" height="64"/>
        <namedColor name="paleGrey">
            <color red="0.94901960784313721" green="0.94901960784313721" blue="0.95294117647058818" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
