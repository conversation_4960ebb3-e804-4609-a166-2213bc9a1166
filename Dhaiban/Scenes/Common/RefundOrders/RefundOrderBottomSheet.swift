//
//  RefundOrderBottomSheet.swift
//  Dhaiban
//
//  Created by osx on 17/04/2024.
//

import UIKit

final class RefundOrderBottomSheet: UIViewController, ReturnProductDelegate {
    func refundSentSuccessfully() {
        dismiss(animated: true)
    }
    
    // MARK: Outlets
    
    @IBOutlet weak var titleLabel: UILabel!
    @IBOutlet weak var contantView: UIView!
    @IBOutlet weak var productImageView: UIImageView!
    @IBOutlet weak var orderNumberLabel: UILabel!
    @IBOutlet weak var orderNumberValue: UILabel!
    
    @IBOutlet weak var productLabel: UILabel!
    @IBOutlet weak var productValue: UILabel!
    
    @IBOutlet weak var qtyToRfundLabel: UILabel!
    @IBOutlet weak var qtyToRfundValue: UILabel!
    @IBOutlet weak var plusButton: UIButton!
    @IBOutlet weak var minusButton: UIButton!
    
    @IBOutlet weak var paymentDetailsLabel: UILabel!
    @IBOutlet weak var paymentDetailsValue: UILabel!
    
    @IBOutlet weak var cancelButton: UIButton!
    @IBOutlet weak var sendButton: UIButton!
    
    // MARK: Properties
    
    weak var coordinator: DashboardCoordinatorProtocol?
    private var type: BottomAlertType
    private var items: OrderProduct
    private let currencySymbol = ServiceLocator.keyValueWrapper.fetchCurrencyModel()?.symbol
    private let currencyExchange = ServiceLocator.keyValueWrapper.fetchCurrencyModel()?.exchangeRate
    private var onTappedIncreaseButton: (()->()) = { }
    private var onTappedDecreaseButton: (()->()) = { }
    private var updatedQuantity: Int = 0
    var addressId: Int = 0
    var orderNumber: String = ""

    // MARK: Init
        
    init(
        type: BottomAlertType,
        items: OrderProduct,
        addressId: Int,
        orderNumber: String
    ) {
        self.type = type
        self.items = items
        self.orderNumber = orderNumber
        self.addressId = addressId
        super.init(nibName: nil, bundle: nil)
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        configureUI()
        configureLocalized()
        configureData()
    }
    
    // MARK: Actions
    
    @IBAction func didTappedOnSendButton(_ sender: UIButton) {
        let viewController = ReturnProductViewController(
            type: .login,
            items: items,
            viewModel: RefundOrdersViewModel(),
            addressId: addressId,
            orderNumber: orderNumber
        )
        viewController.delegate = self // Set the delegate
        viewController.modalPresentationStyle = .formSheet
        viewController.modalTransitionStyle = .coverVertical
        self.present(viewController, animated: true)
    }
    
    @IBAction func CancelButton(_ sender: UIButton) {
        dismiss(animated: true)
    }

    @IBAction func plusButtonTapped(_ sender: UIButton) {
        if var qtyToRefund = Int(qtyToRfundValue.text ?? ""),
           qtyToRefund < (items.quantity ?? 1) {
            qtyToRefund += 1
            qtyToRfundValue.text = "\(qtyToRefund)"
            handleQtyToRefundChange(quantity: qtyToRefund)
        }
    }

    @IBAction func minusButtonTapped(_ sender: UIButton) {
        if var qtyToRefund = Int(qtyToRfundValue.text ?? ""),
           qtyToRefund >= (items.quantity ?? 1) {
            qtyToRefund -= 1
            handleQtyToRefundChange(quantity: qtyToRefund)
        }
    }

    // MARK: Private Methods
    
    private func configureUI() {
        contantView.layer.cornerRadius = 12
        titleLabel.applyStyle(.textStyle34)
        orderNumberLabel.applyStyle(.textStyle52)
        orderNumberValue.applyStyle(.textStyle67)
        productLabel.applyStyle(.textStyle52)
        productValue.applyStyle(.textStyle67)
        qtyToRfundLabel.applyStyle(.textStyle52)
        qtyToRfundValue.applyStyle(.textStyle67)
        paymentDetailsLabel.applyStyle(.textStyle52)
        paymentDetailsValue.applyStyle(.textStyle67)
        productImageView.layer.cornerRadius = 12
//        plusButton.applyStyle(.plus)
        minusButton.applyStyle(.plus)
        minusButton.layer.cornerRadius = 7
        sendButton.applyStyle(.confirm)
        cancelButton.applyStyle(.secondary)
    }

    private func configureLocalized() {
        orderNumberLabel.text = appLanguageKeys?.orderNumber
        titleLabel.text = appLanguageKeys?.refundItems
        productLabel.text = appLanguageKeys?.returnProduct
        qtyToRfundLabel.text = appLanguageKeys?.qtyToRefund
        paymentDetailsLabel.text = appLanguageKeys?.paymentDetails
        sendButton.setTitle(appLanguageKeys?.next, for: .normal)
        cancelButton.setTitle(appLanguageKeys?.back, for: .normal)
    }
    
    private func configureData() {
        orderNumberValue.text = "\(orderNumber)"
        productValue.text = items.productTitle
        handleQtyToRefundChange(quantity: 1)
        productImageView.setImage(
            imagePath: items.photo,
            placeholder: .placeholderProduct
        )
    }

    private func handleQtyToRefundChange(quantity: Int) {
        var total = (items.price ?? 0.0) * Double(quantity)
        total = total * (currencyExchange ?? 1)
        total = total.roundedToTwoDecimalPlaces()
        paymentDetailsValue.text = "\(total) \(currencySymbol ?? "$")"

        qtyToRfundValue.text = "\(quantity)"
    }
}

extension RefundOrderBottomSheet {
    func onTappedIncreaseButton(_ onTapped: @escaping () -> Void) {
        self.onTappedIncreaseButton = onTapped
    }
    
    func onTappedDecreaseButton(_ onTapped: @escaping () -> Void) {
        self.onTappedDecreaseButton = onTapped
    }
}
