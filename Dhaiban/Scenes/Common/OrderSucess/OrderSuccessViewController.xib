<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="23094" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23084"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="OrderSuccessViewController" customModule="Dhaiban" customModuleProvider="target">
            <connections>
                <outlet property="backButton" destination="1PO-Jz-2qh" id="pJh-Gw-IUy"/>
                <outlet property="contantView" destination="wSa-Qy-BKR" id="nAR-SY-x3V"/>
                <outlet property="noOrderLabel" destination="iyO-Le-tXa" id="Sqf-jM-mnW"/>
                <outlet property="paymentImageView" destination="JlP-Ut-kdU" id="edw-Sc-cfN"/>
                <outlet property="paymentTitle" destination="G7d-Mb-e6d" id="FJR-PC-lz6"/>
                <outlet property="trackOrderButton" destination="52E-8d-KGV" id="K0a-1O-bgG"/>
                <outlet property="view" destination="CIw-Z9-jb5" id="CVU-r3-gip"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="CIw-Z9-jb5">
            <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="wSa-Qy-BKR">
                    <rect key="frame" x="39.333333333333343" y="196" width="314.33333333333326" height="460"/>
                    <subviews>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="title" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="G7d-Mb-e6d">
                            <rect key="frame" x="12" y="12" width="290.33333333333331" height="40"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="40" id="JEX-se-KGy"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="JlP-Ut-kdU">
                            <rect key="frame" x="16" y="68" width="282.33333333333331" height="200"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="200" id="I9C-Eb-Mua"/>
                            </constraints>
                        </imageView>
                        <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="Fou-5P-6Sf">
                            <rect key="frame" x="16" y="344" width="282.33333333333331" height="92"/>
                            <subviews>
                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="52E-8d-KGV">
                                    <rect key="frame" x="0.0" y="0.0" width="282.33333333333331" height="42"/>
                                    <state key="normal" title="Button"/>
                                    <buttonConfiguration key="configuration" style="plain" title="Button"/>
                                    <connections>
                                        <action selector="trackOrderLabel:" destination="-1" eventType="touchUpInside" id="2Nv-uB-nWq"/>
                                    </connections>
                                </button>
                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="1PO-Jz-2qh">
                                    <rect key="frame" x="0.0" y="50" width="282.33333333333331" height="42"/>
                                    <state key="normal" title="Button"/>
                                    <buttonConfiguration key="configuration" style="plain" title="Button"/>
                                    <connections>
                                        <action selector="backButton:" destination="-1" eventType="touchUpInside" id="DrD-tr-Ldx"/>
                                    </connections>
                                </button>
                            </subviews>
                        </stackView>
                        <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="subtitle" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="3" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="iyO-Le-tXa">
                            <rect key="frame" x="12" y="292" width="290.33333333333331" height="28"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="28" id="S7D-aM-Kb4"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    <constraints>
                        <constraint firstAttribute="bottom" secondItem="Fou-5P-6Sf" secondAttribute="bottom" constant="24" id="0y2-3I-3aU"/>
                        <constraint firstItem="Fou-5P-6Sf" firstAttribute="leading" secondItem="wSa-Qy-BKR" secondAttribute="leading" constant="16" id="1ZR-We-aTC"/>
                        <constraint firstItem="JlP-Ut-kdU" firstAttribute="top" secondItem="G7d-Mb-e6d" secondAttribute="bottom" constant="16" id="A0m-QR-YXu"/>
                        <constraint firstAttribute="trailing" secondItem="JlP-Ut-kdU" secondAttribute="trailing" constant="16" id="NAo-Sd-RVG"/>
                        <constraint firstAttribute="trailing" secondItem="G7d-Mb-e6d" secondAttribute="trailing" constant="12" id="Tp2-wx-Hao"/>
                        <constraint firstItem="iyO-Le-tXa" firstAttribute="leading" secondItem="wSa-Qy-BKR" secondAttribute="leading" constant="12" id="Utp-Xl-i8q"/>
                        <constraint firstAttribute="trailing" secondItem="Fou-5P-6Sf" secondAttribute="trailing" constant="16" id="V9B-ki-bfl"/>
                        <constraint firstAttribute="height" constant="460" id="XAz-vV-Gfi"/>
                        <constraint firstItem="iyO-Le-tXa" firstAttribute="top" secondItem="JlP-Ut-kdU" secondAttribute="bottom" constant="24" id="cRp-a8-iEg"/>
                        <constraint firstItem="JlP-Ut-kdU" firstAttribute="leading" secondItem="wSa-Qy-BKR" secondAttribute="leading" constant="16" id="gFO-I7-HfH"/>
                        <constraint firstItem="G7d-Mb-e6d" firstAttribute="leading" secondItem="wSa-Qy-BKR" secondAttribute="leading" constant="12" id="r8t-lP-2Hz"/>
                        <constraint firstItem="Fou-5P-6Sf" firstAttribute="top" secondItem="iyO-Le-tXa" secondAttribute="bottom" constant="24" id="ssK-zf-rtQ"/>
                        <constraint firstAttribute="trailing" secondItem="iyO-Le-tXa" secondAttribute="trailing" constant="12" id="vOE-YM-qeA"/>
                        <constraint firstItem="G7d-Mb-e6d" firstAttribute="top" secondItem="wSa-Qy-BKR" secondAttribute="top" constant="12" id="xQJ-yb-Hlh"/>
                        <constraint firstAttribute="trailing" secondItem="G7d-Mb-e6d" secondAttribute="trailing" constant="12" id="ze6-Kn-mN8"/>
                    </constraints>
                </view>
            </subviews>
            <viewLayoutGuide key="safeArea" id="uNd-if-oSh"/>
            <color key="backgroundColor" white="0.0" alpha="0.20000000000000001" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="wSa-Qy-BKR" firstAttribute="centerY" secondItem="CIw-Z9-jb5" secondAttribute="centerY" id="QRb-iK-EBg"/>
                <constraint firstItem="wSa-Qy-BKR" firstAttribute="centerX" secondItem="CIw-Z9-jb5" secondAttribute="centerX" id="ZWi-uW-xVa"/>
                <constraint firstItem="wSa-Qy-BKR" firstAttribute="width" secondItem="CIw-Z9-jb5" secondAttribute="width" multiplier="0.8" id="zmO-UC-a60"/>
            </constraints>
            <point key="canvasLocation" x="339.69465648854958" y="-44.366197183098592"/>
        </view>
    </objects>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
