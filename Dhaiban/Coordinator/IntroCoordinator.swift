//
//  IntroCoordinator.swift
//  Dhaiban
//
//  Created by <PERSON> on 21/01/2024.
//

import UIKit


protocol IntroCoordinatorProtocol: AnyObject {
    func showSplash()
    func showOnboarding()
    func showOnboardingConfigurations()
    func presentBottomListSheet(type: BottomListSheetType, delegate: UIViewController)
    func presentNextCoordinator()
}


class IntroCoordinator: Coordinator {
    let navigationController: UINavigationController
    let children: [Coordinator] = []
    
    private let presentNextCoordinatorClosure: (()->())?
    
    init(presentNextCoordinatorClosure: @escaping () -> Void) {
        navigationController = UINavigationController()
        navigationController.isNavigationBarHidden = true
        self.presentNextCoordinatorClosure = presentNextCoordinatorClosure
    }
    
    func start() {
        showSplash()
    }
}

extension IntroCoordinator: IntroCoordinatorProtocol  {
    func showSplash() {
        let viewController = Sp<PERSON>roller()
        viewController.coordinator = self
        show(viewController: viewController)
    }
    
    func showOnboarding() {
        let viewModel = OnboardingViewModel()
        let viewController = OnboardingViewController(viewModel: viewModel)
        viewController.coordinator = self
        show(viewController: viewController)
    }
    
    func showOnboardingConfigurations() {
        let viewController = OnboardingConfigurationsViewController()
        viewController.coordinator = self
        show(viewController: viewController)
    }
    
    func presentBottomListSheet(type: BottomListSheetType, delegate: UIViewController) {
        presentBottomList(type: type, delegate: delegate)
    }
    
    func presentNextCoordinator() {
        presentNextCoordinatorClosure?()
    }
}
