//
//  DashboardCoordinator.swift
//  Dhaiban
//
//  Created by <PERSON> on 22/01/2024.
//

import UIKit

protocol DashboardCoordinatorProtocol: AnyObject {
    func showDashboard()
    func switchToCartTab()
    func switchToHomeTab()
    func presentBottomAlert(type: BottomAlertType)
    func presentErrorAlert(errorType: AppErrorType)
    func presentBottomListSheet(type: BottomListSheetType, delegate: UI<PERSON>iewController)
    func presentLoginButtonAlert(type: BottomAlertType)
    func presentConfirmPhone(type: BottomAlertType, phone: String)
//    func presentRefundOrderBottomSheet(type: BottomAlertType)
    func presentEditPhone(type: BottomAlertType)
    func presentOrderAlert(orderId: Int)
    func pesentAddCartAlert(type: AppErrorType)
    func presentAddressAlert()
    func showMyProfile()
    func showChangePassword()
    func showStaticPage(type: StaticPageType)
    func showContactUs()
    func showCustomerServices()
    func showSendNewMessage()
    func showPreviousMessages()
    func showFAQTypes()
    func showFAQs(forType faqType: FAQTypeItemModel)
    func showAddress()
    func showConversations()
    func showAddNewAddress(latitude: Double, longitude: Double, postalCode: String)
    func showCoupons()
    func showPayment(paymentDetails: PaymentDetailsModel, addressId: Int, copounCode: String)
    func presentDashboardCoordinator()
    func presentAuthCoordinator()
    func showProductDetails(productId: Int,productTitle: String)
    func showProductDetails(
        productId: Int,
        productTitle: String,
        selectedOptions: SelectedVariantOptions
    )
    func showBrandViewController()
    func showFlashSaleViewController()
    func showFavourites()
    func showCategories()
    func showMapView()
    func backToAddress()
    func showSubcategories(categoryID: Int, categoryTitle: String)
    func showBrandProduct(brandID: Int, brandTitle: String)
    func showGuestPage()
    func showSearchViewController()
    func showOrderView()
    func showEditAddress(addressmodel: AddressModel)
    func showRefund()
    func showWallet()
//    func showTrackOrder()
    func showFilterViewController(categoryId: Int,onFiltersUpdated: @escaping (_ selections: FilterSelections) -> Void)
    func showNotificationsViewController()
    func showTrackOrderViewController(orderId: Int, isRefundable: Bool)
    func showTrackSuccessfullOrderViewController(orderId: Int, isRefundable: Bool)
    func showTrackRefundOrderViewController(refundId: Int)
    func presentReviewProduct(
        orderId: Int,
        orderProduct: OrderProduct
    )
    func showProductConversationViewController(
        orderId: Int,
        orderProduct: OrderProduct
    )
    func showProductConversationViewController(
        conversationId: Int
    )
    func presentWalletAlart(delegate: WalletEnterAlertDelegate)
    func presentMomopayAlert(
        onSaveMomopayMethodDetails: @escaping (
            _ partyId: String,
            _ partyIdType: MomopaymentPartyIdType
        ) -> Void
    )
    func showSendMomopayPayment()
    func presentDialCode(onSelection: @escaping (DialCodeData) -> Void)
    func showLocationSearchSheet(
        title: String,
        provinces: [OriginalData],
        onLocationSelected: @escaping LocationSearchSelected
    )
}

class DashboardCoordinator: Coordinator, AddToProductToCartProtocol {
    func onSelectedItem(id: Int, title: String) {
            showProductDetails(productId: id, productTitle: title)
    }
    
    let navigationController: UINavigationController
    let children: [Coordinator] = []
    
    private let presentAuthCoordinatorClosure: ((_ showIntro: Bool)->())?
    private let presentDashboardCoordinatorClosure: (()->())?

    init(
        presentAuthCoordinatorClosure: @escaping (_ showIntro: Bool) -> Void,
        presentDashboardCoordinatorClosure: @escaping () -> Void
    ) {
        navigationController = UINavigationController()
        navigationController.isNavigationBarHidden = true

        self.presentDashboardCoordinatorClosure = presentDashboardCoordinatorClosure
        self.presentAuthCoordinatorClosure = presentAuthCoordinatorClosure
    }
    
    func start() {
        showDashboard()
        switchToHomeTab()
    }
}

protocol DashboardCoordinating: AnyObject, NotificationTappingHandlder {
    var coordinator: DashboardCoordinatorProtocol? { get set }
}

extension DashboardCoordinating {
    func notificationsButtonTapped() {
        if ServiceLocator.keyValueWrapper.fetchUserModel() == nil {
            self.coordinator?.presentLoginButtonAlert(type: .login)
        } else {
            self.coordinator?.showNotificationsViewController()
        }
    }
}

extension DashboardCoordinator: DashboardCoordinatorProtocol {
    func pesentAddCartAlert(type: AppErrorType) {
        let viewController = AddCartViewController(errorType: type)
        viewController.coordinator = self
        viewController.modalPresentationStyle = .overFullScreen
        viewController.modalTransitionStyle = .crossDissolve
        navigationController.present(viewController, animated: true)
    }
    
    func presentAddressAlert() {
        let viewController = AddressAlertViewController()
        viewController.coordinator = self
        viewController.modalPresentationStyle = .overFullScreen
        viewController.modalTransitionStyle = .crossDissolve
        navigationController.present(viewController, animated: true)
    }
    
    func showDashboard() {
        let homeViewController = getHomeViewController()
        let categoriesViewController = getCategoriesViewController()
        let cartViewController = getCartViewController()
        let profileViewController = getProfileViewController()
        let mainTabBar = MainTabBarController(
            tabbarViewControllers: [
                homeViewController,
                categoriesViewController,
                cartViewController,
                profileViewController
            ],
            cartViewModel: cartViewController.viewModel
        )
        mainTabBar.coordinator = self
        show(viewController: mainTabBar)
    }
    
    func switchToHomeTab() {
        guard let mainTabBarController = findMainTabBarController()
        else { return }

        mainTabBarController.selectedIndex = 0
        navigationController.popToRootViewController(animated: false)
    }
    
    func switchToCartTab() {
        guard let mainTabBarController = findMainTabBarController()
        else { return }

        mainTabBarController.selectedIndex = 2
        navigationController.popToRootViewController(animated: false)
    }
    
    func switchToProfileTab(animated: Bool = false) {
        guard let mainTabBarController = findMainTabBarController()
        else { return }

        mainTabBarController.selectedIndex = 3
        navigationController.popToRootViewController(animated: false)
    }
    
    private func findMainTabBarController() -> MainTabBarController? {
        self.navigationController.viewControllers.first(
            where: { viewController in
                viewController is MainTabBarController
            }
        ) as? MainTabBarController
    }

    func presentErrorAlert(errorType: AppErrorType) {
        presentErrorAlertViewController(errorType: errorType)
    }
    
    func presentBottomAlert(type: BottomAlertType) {
        let viewController = SimpleBottomAlert(type: type)
        viewController.coordinator = self
        viewController.modalPresentationStyle = .overFullScreen
        viewController.modalTransitionStyle = .crossDissolve
        navigationController.present(viewController, animated: true)
    }
    func presentLoginButtonAlert(type: BottomAlertType){
        let viewController = LoginBottomSheet(type: type)
        viewController.coordinator = self
        viewController.modalPresentationStyle = .overFullScreen
        viewController.modalTransitionStyle = .crossDissolve
        navigationController.present(viewController, animated: true)
    }
    
//    func presentRefundOrderBottomSheet(type: BottomAlertType) {
//        let viewController = RefundOrderBottomSheet(type: type)
//        viewController.coordinator = self
//        viewController.modalPresentationStyle = .overFullScreen
//        viewController.modalTransitionStyle = .crossDissolve
//        navigationController.present(viewController, animated: true)
//    }

    func presentEditPhone(type: BottomAlertType) {
        let viewModel = MyProfileViewModel()
        let viewController = EditPhoneViewController(viewModel: viewModel, type: type)
        viewController.coordinator = self
        viewController.modalPresentationStyle = .formSheet
        viewController.modalTransitionStyle = .coverVertical
        navigationController.present(viewController, animated: true)
        
    }
    
    func presentConfirmPhone(type: BottomAlertType, phone: String) {
        let viewModel = MyProfileViewModel()
        let viewController =  EditPasswordViewController(viewModel: viewModel, type: type, phone: phone)
        viewController.coordinator = self
        viewController.modalPresentationStyle = .formSheet
        viewController.modalTransitionStyle = .coverVertical
        navigationController.present(viewController, animated: true)
    }
   
    
    func presentBottomListSheet(type: BottomListSheetType, delegate: UIViewController) {
        presentBottomList(type: type, delegate: delegate)
    }
    
    func presentOrderAlert(orderId: Int) {
        let viewController = OrderSuccessViewController(orderId: orderId)
        viewController.coordinator = self
        viewController.modalPresentationStyle = .overFullScreen
        viewController.modalTransitionStyle = .crossDissolve
        navigationController.present(viewController, animated: true)
    }
    
    func showMyProfile() {
        let viewModel = MyProfileViewModel()
        let viewController = MyProfileViewController(viewModel: viewModel)
        viewController.coordinator = self
        show(viewController: viewController)
    }
    
    func showChangePassword() {
        let viewModel = ChangePasswordViewModel()
        let viewController = ChangePasswordViewController(viewModel: viewModel)
        viewController.coordinator = self
        viewController.modalPresentationStyle = .formSheet
        viewController.modalTransitionStyle = .coverVertical
        navigationController.present(viewController, animated: true)
    }
    
    func showStaticPage(type: StaticPageType) {
        let viewModel = StaticPageViewModel(type: type)
        let viewController = StaticPageViewController(viewModel: viewModel)
        viewController.coordinator = self
        show(viewController: viewController)
    }
    
    func showContactUs() {
        let viewModel = ContactUsViewModel()
        let viewController = ContactUsViewController(viewModel: viewModel)
        viewController.coordinator = self
        show(viewController: viewController)
    }
    
    func showCustomerServices() {
        let viewController = CustomerServiceViewController()
        viewController.coordinator = self
        show(viewController: viewController)
    }
    
    func showSendNewMessage() {
        let viewController = CustomerServiceViewController()
        viewController.coordinator = self
        show(viewController: viewController)
    }
    
    func showPreviousMessages() {
        let viewModel = PreviousMessagesViewModel()
        let viewController = PreviousMessagesViewController(viewModel: viewModel)
        viewController.coordinator = self
        show(viewController: viewController)
    }
    
    func showFAQTypes() {
        let viewModel = FAQTypesViewModel()
        let viewController = FAQTypesViewController(viewModel: viewModel)
        viewController.coordinator = self
        show(viewController: viewController)
    }
    
    func showFAQs(forType faqType: FAQTypeItemModel) {
        let viewModel = FAQsViewModel(typeId: faqType.id)
        let viewController = FAQsViewController(viewModel: viewModel)
        viewController.coordinator = self
        show(viewController: viewController)
    }
    
    func showAddress() {
        let viewModel = AddressViewModel()
        let viewController = AddressViewController(viewModel: viewModel)
        viewController.coordinator = self
        show(viewController: viewController)
    }
    
    func backToAddress() {
        for viewController in navigationController.viewControllers {
            if let addressViewController = viewController as? AddressViewController {
                popToViewController(addressViewController)
//                show(viewController: addressViewController)
                return
            }
        }
    }
    
    func showConversations() {
        let viewController = ConversationsViewController()
        viewController.coordinator = self
        show(viewController: viewController)
    }
    
    func showAddNewAddress(latitude: Double, longitude: Double, postalCode: String) {
        let viewModel = AddNewAddressViewModel()
        let viewController = AddNewAddressViewController(viewModel: viewModel, latitude: latitude, longitude: longitude, postalCode: postalCode)
        viewController.coordinator = self
        show(viewController: viewController)
    }

    
    func showCoupons() {
        let viewModel = CouponsViewModel()
        let viewController = CouponsViewController(viewModel: viewModel)
        viewController.coordinator = self
        show(viewController: viewController)
    }
    
    func showPayment(paymentDetails: PaymentDetailsModel, addressId: Int, copounCode: String) {
        let viewModel = PaymentViewModel(
            paymentDetails: paymentDetails,
            addressId: addressId,
            copounCode: copounCode
        )
        let walletViewModel = WalletViewModel()
        let viewController = PaymentViewController(viewModel: viewModel, walletViewModel: walletViewModel)
        viewController.coordinator = self
        show(viewController: viewController)
    }
    
    func presentDashboardCoordinator() {
        presentDashboardCoordinatorClosure?()
    }
    
    func presentAuthCoordinator() {
        presentAuthCoordinatorClosure?(false)
    }
    func showProductDetails(productId: Int, productTitle: String) {
        let viewModel = ProductDetailsViewModel(productId: productId, productTitle: productTitle)
        let viewController = ProductDetailsViewController(viewModel: viewModel)
        viewController.coordinator = self
        show(viewController: viewController)
    }
    func showProductDetails(
        productId: Int,
        productTitle: String,
        selectedOptions: SelectedVariantOptions
    ) {
        let viewModel = ProductDetailsViewModel(
            productId: productId, 
            productTitle: productTitle,
            selectedOptions: selectedOptions
        )
        let viewController = ProductDetailsViewController(viewModel: viewModel)
        viewController.coordinator = self
        show(viewController: viewController)
    }
    func showBrandViewController() {
        let viewModel = HomeViewModel()
        let viewController = BrandViewController(viewModel: viewModel)
        show(viewController: viewController)
    }
    func showFlashSaleViewController() {
        let viewModel = HomeViewModel()
        let viewController = FlashSaleViewController(viewModel: viewModel)
        viewController.delegate = self
        show(viewController: viewController)
    }
    func showFavourites(){
        let viewModel = FavouritesViewModel()
        let viewController = FavouritesViewController(viewModel: viewModel)
        viewController.coordinator = self
        show(viewController: viewController)
    }
    func showCategories() {
       let viewModel = CategoriesViewModel()
        let viewController = CategoriesViewController(viewModel: viewModel)
        viewController.coordinator = self
        show(viewController: viewController)
    }
    func showMapView() {
        let viewController = MapAddressViewController()
        viewController.coordinator = self
        show(viewController: viewController)
    }
    
    func showSubcategories(categoryID: Int, categoryTitle: String) {
        let viewModel = SubCategoriesViewModel()
        let viewController = SubcategoriesViewController(viewModel: viewModel, categoryID: categoryID, categoryTitle: categoryTitle)
        viewController.coordinator = self
        show(viewController: viewController)
    }
    func showBrandProduct(brandID: Int, brandTitle: String) {
        let viewModel = HomeViewModel()
        let viewController = InnerBrandViewController(viewModel: viewModel, brandID: brandID, brandTitle: brandTitle)
        viewController.coordinator = self
        show(viewController: viewController)
    }
    func showGuestPage() {
        let viewController = GuestPageViewController()
        viewController.coordinator = self
        show(viewController: viewController)
    }
    
    func showSearchViewController(){
        let viewModel = HomeViewModel()
        let viewController = SearchViewController(viewModel: viewModel)
        viewController.coordinator = self
        show(viewController: viewController)
    }
    func showOrderView(){
        let viewModel = OrdersViewModel()
        let viewController = OrdersViewController(viewModel: viewModel)
        viewController.coordinator = self
        show(viewController: viewController)
    }
//    func showTrackOrder(){
//        let viewModel = TrackOrderViewModel()
//        let viewController = TrackOrderViewController(viewModel: viewModel)
//        viewController.coordinator = self
//        show(viewController: viewController)
//    }
    func showEditAddress(addressmodel: AddressModel) {
        let viewModel = AddNewAddressViewModel()
        let viewController = ChangeAddressViewController(viewModel: viewModel, addressModel: addressmodel)
        viewController.coordinator = self
        show(viewController: viewController)
    }
    func showRefund(){
        let viewModel = RefundOrdersViewModel()
        let viewController = RefundOrdersViewController(viewModel: viewModel)
        viewController.coordinator = self
        show(viewController: viewController)
    }
    func showWallet(){
        let viewModel = WalletViewModel()
        let viewController = WalletViewController(viewModel: viewModel)
        viewController.coordinator = self
        show(viewController: viewController)
    }
    
    
//    func showEditPhone(){
//        let viewModel = MyProfileViewModel()
//        let viewController = EditPhoneViewController(viewModel: viewModel)
//        viewController.coordinator = self
//        show(viewController: viewController)
//    }
    
    func showFilterViewController(categoryId: Int,onFiltersUpdated: @escaping (_ selections: FilterSelections) -> Void) {
        let filterViewController = FilterViewController(categoryID: categoryId)
        filterViewController.onFiltersUpdated = onFiltersUpdated
        show(viewController: filterViewController)
    }

    func showNotificationsViewController() {
        let viewController = NotificationsViewController()
        viewController.coordinator = self
        show(viewController: viewController)
    }

    func showTrackSuccessfullOrderViewController(orderId: Int, isRefundable: Bool) {
        let viewController = TrackOrderViewController(
            viewModel: TrackOrderViewModel(orderId: orderId), isRefundable: isRefundable
        )
        viewController.coordinator = self
        switchToProfileTab()
        show(viewController: viewController)
    }

    func showTrackOrderViewController(orderId: Int, isRefundable: Bool) {
        let viewController = TrackOrderViewController(
            viewModel: TrackOrderViewModel(orderId: orderId), isRefundable: isRefundable
        )
        viewController.coordinator = self
        show(viewController: viewController)
    }

    func showTrackRefundOrderViewController(refundId: Int) {
        let viewController = TrackRefundOrderViewController(
            viewModel: TrackRefundOrderViewModel(refundId: refundId)
        )
        viewController.coordinator = self
        show(viewController: viewController)
    }

    func presentReviewProduct(
        orderId: Int,
        orderProduct: OrderProduct
    ) {
        let viewController = ReviewProductViewController()
        viewController.coordinator = self
        viewController.orderId = orderId
        viewController.orderProduct = orderProduct
        viewController.modalPresentationStyle = .overFullScreen
        viewController.modalTransitionStyle = .crossDissolve
        navigationController.present(viewController, animated: true)
    }
    func showProductConversationViewController(
        orderId: Int,
        orderProduct: OrderProduct
    ) {
        let viewController = ProductConversationViewController()
        viewController.coordinator = self
        viewController.viewModel = ProductConversationByOrderProductViewModel(
            orderId: orderId,
            orderProduct: orderProduct
        )
        show(viewController: viewController)
    }
    func showProductConversationViewController(
        conversationId: Int
    ) {
        let viewController = ProductConversationViewController()
        viewController.coordinator = self
        viewController.viewModel = ProductConversationByConversationIdViewModel(
            conversationId: conversationId
        )
        show(viewController: viewController)
    }
    func presentWalletAlart(delegate: WalletEnterAlertDelegate) {
        // Replace placeholders with actual values
        let errorType: AppErrorType = .genericError
        let viewModel: WalletViewModelType = WalletViewModel()
        let coordinator: DashboardCoordinatorProtocol = self
        
        let viewController = WalletEnterAlert(errorType: errorType, viewModel: viewModel, coordinator: coordinator)
        viewController.delegate = delegate
        viewController.modalPresentationStyle = .overFullScreen
        viewController.modalTransitionStyle = .crossDissolve
        navigationController.present(viewController, animated: true)
    }

    func presentMomopayAlert(
        onSaveMomopayMethodDetails: @escaping (
            _ partyId: String,
            _ partyIdType: MomopaymentPartyIdType
        ) -> Void
    ) {
        let viewController = MomopayAlert()
        viewController.onSaveMomopayMethodDetails = onSaveMomopayMethodDetails
        viewController.modalPresentationStyle = .overFullScreen
        viewController.modalTransitionStyle = .crossDissolve
        navigationController.present(viewController, animated: true)
    }

    func showSendMomopayPayment() {
        let viewController = SendMomopayPaymentViewController()
        viewController.viewModel = SendMomopayPaymentViewModel()
        viewController.coordinator = self

        show(viewController: viewController)
    }
    
    func presentDialCode(onSelection: @escaping (DialCodeData) -> Void) {
        let viewModel = DialCodeSearchViewModel()
        let viewController = DialCodeSearchViewController(viewModel: viewModel)
        viewController.onSelection = onSelection
        presentViewController(viewController: viewController)
    }

    func showLocationSearchSheet(
        title: String,
        provinces: [OriginalData],
        onLocationSelected: @escaping LocationSearchSelected
    ) {
        let screen = LocationSearchScreen()
        screen.title = title
        screen.viewModel = LocationSearchViewModel(
            title: title,
            data: provinces.map {
                LocationSearchResultVM(id: $0.id, name: $0.title)
            }
        )
        screen.onLocationSelected = {
            onLocationSelected(.init(id: $0.id, name: $0.name))
        }
        
        presentViewController(viewController: screen)
    }

//    func showCitySearchSheet(
//        title: String,
//        countryCode: String,
//        onLocationSelected: @escaping LocationSearchSelected
//    ) {
//        let screen = LocationSearchScreen()
//        screen.viewModel = CitySearchViewModel(title: title, countryCode: countryCode)
//        screen.onLocationSelected = {
//            onLocationSelected(.init(code: $0.code, name: $0.name))
//        }
//        router.presentSheet(screen)
//    }
}


// MARK: Tabbar view controllers

extension DashboardCoordinator {
    func getHomeViewController() -> HomeViewController {
        let viewModel = HomeViewModel()
        let viewController = HomeViewController(viewModel: viewModel)
        viewController.coordinator = self
        return viewController
    }
    
    func getCategoriesViewController() -> CategoriesViewController {
        let viewModel = CategoriesViewModel()
        let viewController = CategoriesViewController(viewModel: viewModel)
        viewController.coordinator = self
        return viewController
    }
    
    func getCartViewController() -> CartViewController {
        getCartViewController(viewModel: CartViewModel())
    }
    
    func getCartViewController(viewModel: CartViewModelType) -> CartViewController {
        let viewController = CartViewController(viewModel: viewModel)
        viewController.coordinator = self
        return viewController
    }
    
    func getProfileViewController() -> ProfileViewController {
        let viewModel = ProfileViewModel()
        let viewController = ProfileViewController(viewModel: viewModel)
        viewController.coordinator = self
        return viewController
    }
}
