//
//  AppCoordinator.swift
//  Dhaiban
//
//  Created by <PERSON> on 21/01/2024.
//

import UIKit


class AppCoordinator: Coordinator {
    let navigationController: UINavigationController
    let window: UIWindow
    private(set) var children: [Coordinator] = []

    var onDashboardCoordinatorStarted: (() -> Void)?

    init(
        window: UIWindow, 
        navigationController: UINavigationController,
        onDashboardCoordinatorStarted: (() -> Void)? = nil
    ) {
        self.window = window
        self.navigationController = navigationController
        self.onDashboardCoordinatorStarted = onDashboardCoordinatorStarted
    }
    
    func start() {
        presentIntroCoordinator()
    }
}

private extension AppCoordinator {
    func presentIntroCoordinator() {
        let coordinator = IntroCoordinator { [weak self] in
            if ServiceLocator.keyValueWrapper.fetchUserModel() == nil {
                self?.presentAuthCoordinator(showIntro: true)
            } else {
                self?.presentDashboardCoordinator()
            }
        }
        
        children.removeAll()
        startCoordinator(coordinator)
        replaceWindowRootViewController(coordinator.navigationController)
    }
    
    func presentAuthCoordinator(showIntro: Bool) {
        let coordinator = AuthCoordinator(
            showIntro: showIntro,
            presentDashboardCoordinatorClosure: { [weak self] in
                self?.presentDashboardCoordinator()
            }
        )

        children.removeAll()
        startCoordinator(coordinator)
        replaceWindowRootViewController(coordinator.navigationController)
    }
    
    func presentDashboardCoordinator() {
        let coordinator = DashboardCoordinator(
            presentAuthCoordinatorClosure: { [weak self] showIntro in
                guard let self = self else { return }
                self.presentAuthCoordinator(showIntro: showIntro)
            },
            presentDashboardCoordinatorClosure: { [weak self] in
                guard let self = self else { return }
                self.presentDashboardCoordinator()
            }
        )

        children.removeAll()
        startCoordinator(coordinator)
        replaceWindowRootViewController(coordinator.navigationController)
        onDashboardCoordinatorStarted?()
    }
}


//MARK: start coordinator

private extension AppCoordinator {
    func startCoordinator(_ coordinator: Coordinator) {
        children = [coordinator]
        coordinator.start()
    }
}

// MARK: replace root view controller Window

private extension AppCoordinator {
    func replaceWindowRootViewController(_ viewController: UIViewController) {
        UIView.transition(with: window, duration: 0.3, options: [.transitionCrossDissolve], animations: {
            self.window.rootViewController = viewController
            self.window.makeKeyAndVisible()
        }, completion: { _ in
            // maybe do something on completion here
        })
    }
}
