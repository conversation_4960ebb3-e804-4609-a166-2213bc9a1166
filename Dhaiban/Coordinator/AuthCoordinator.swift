//
//  AuthCoordinator.swift
//  Dhaiban
//
//  Created by <PERSON> on 21/01/2024.
//

import UIKit


protocol AuthCoordinatorProtocol: AnyObject {
    // TODO: seems not used anywhere, delete whenever possible
//    func presentAuthCoordinator()
    func showIntroAuth()
    func showLogin()
    func showSignup()
    func showForgotPassword()
    func showResetPassword(code: Int, email: String)
    func showOTP(code: Int, email: String)
    func presentErrorAlert(errorType: AppErrorType)
    func presentDashboardCoordinator()
    func showVerficationOTP()
    func presentDialCode(onSelection: @escaping (DialCodeData) -> Void)
}


class AuthCoordinator: Coordinator {
    let navigationController: UINavigationController
    let children: [Coordinator] = []

//    private let presentAuthCoordinatorClosure: (() -> ())?
    private let showIntro: Bool
    private let presentDashboardCoordinatorClosure: (() -> ())?
    
    init(
//        presentA<PERSON>oordinatorClosure: @escaping () -> Void,
        showIntro: <PERSON><PERSON>,
        presentDashboardCoordinatorClosure: @escaping () -> Void
    ) {
        navigationController = UINavigationController()
        navigationController.isNavigationBarHidden = true
        
//        self.presentAuthCoordinatorClosure = presentAuthCoordinatorClosure
        self.showIntro = showIntro
        self.presentDashboardCoordinatorClosure = presentDashboardCoordinatorClosure
    }
    
    func start() {
        if showIntro {
            showIntroAuth()

        } else {
            showLogin()
        }
    }
}


extension AuthCoordinator: AuthCoordinatorProtocol  {
    // TODO: seems not used anywhere, delete whenever possible
//    func presentAuthCoordinator() {
//        presentAuthCoordinatorClosure?()
//    }

    func showIntroAuth() {
        let viewController = IntroAuthViewController()
        viewController.coordinator = self
        show(viewController: viewController)
    }
    
    func showLogin() {
        let viewModel = LoginViewModel()
        let viewController = LoginViewController(viewModel: viewModel)
        viewController.coordinator = self
        show(viewController: viewController)
    }
    
    func showSignup() {
        let viewModel = SignupViewModel()
        let viewController = SignupViewController(viewModel: viewModel)
        viewController.coordinator = self
        show(viewController: viewController)
    }
    
    func showForgotPassword() {
        let viewModel = ForgotPasswordViewModel()
        let viewController = ForgotPasswordViewController(viewModel: viewModel)
        viewController.coordinator = self
        show(viewController: viewController)
    }
    
    func showResetPassword(code: Int, email: String) {
        let viewModel = ResetPasswordViewModel(code: code, email: email)
        let viewController = ResetPasswordViewController(viewModel: viewModel)
        viewController.coordinator = self
        show(viewController: viewController)
    }
    
    func showOTP(code: Int, email: String) {
        let viewModel = OTPViewModel(code: code, email: email)
        let viewController = OTPViewController(viewModel: viewModel)
        viewController.coordinator = self
        show(viewController: viewController)
    }
    func showVerficationOTP() {
        let code = 0
        let email = ""
        let viewModel = VerficationOTPViewModel(code: code, email: email)
        let viewController = VerficationOTPViewController(viewModel: viewModel)
        viewController.coordinator = self
        show(viewController: viewController)
        
    }
    
    
    func presentDialCode(onSelection: @escaping (DialCodeData) -> Void) {
        let viewModel = DialCodeSearchViewModel()
        let viewController = DialCodeSearchViewController(viewModel: viewModel)
        viewController.onSelection = onSelection
        presentViewController(viewController: viewController)
    }
    
    func presentErrorAlert(errorType: AppErrorType) {
        presentErrorAlertViewController(errorType: errorType)
    }
    
    func presentDashboardCoordinator() {
        presentDashboardCoordinatorClosure?()
    }
}
