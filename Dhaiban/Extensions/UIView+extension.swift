//
//  UIView+extension.swift
//  Dhaiban
//
//  Created by <PERSON> on 22/01/2024.
//

import UIKit



/// Add round in specific corners.
///
extension UIView {
   func roundCorners(corners: UIRectCorner, radius: CGFloat) {
        let path = UIBezierPath(roundedRect: bounds, byRoundingCorners: corners, cornerRadii: CGSize(width: radius, height: radius))
        let mask = CAShapeLayer()
        mask.path = path.cgPath
        layer.mask = mask
    }
}



// MARK: make view as circle

extension UIView {
    func makeCircleView() {
        self.layer.cornerRadius = self.layer.bounds.width / 2
        self.clipsToBounds = true
    }
}


/// Animating when clicked on view.
///
extension UIView {
    func applyAnimationClicked() {
        UIView.animate(withDuration: 0.1, delay: 0, usingSpringWithDamping: 0.5, initialSpringVelocity: 0.5, options: .curveEaseIn) {
            
            self.transform = CGAffineTransform(scaleX: 0.92, y: 0.92)
        } completion: { _ in
            UIView.animate(withDuration: 0.1, delay: 0, usingSpringWithDamping: 0.4, initialSpringVelocity: 2, options: .curveEaseIn) {
                
                self.transform = CGAffineTransform(scaleX: 1, y: 1)
            } completion: { _ in
            }
        }
    }
}



// MARK: Apply shadow for view

extension UIView {
    func applyNotificationCellShadow() {
        applyShadow(
            cornerRadius: 5,
            masksToBounds: false,
            shadowOpacity: 0.175,
            shadowRadius: 4.0,
            shadowOffset: .init(width: 0, height: 1),
            shadowColor: .black
        )
    }

    func removeShadow() {
        self.layer.cornerRadius = 0
        self.layer.masksToBounds = false

        self.layer.shadowOpacity = .zero
        self.layer.shadowRadius = .zero
        self.layer.shadowOffset = .zero
        self.layer.shadowColor = nil
        self.layer.shadowPath = nil
    }

     func applyShadow(
        cornerRadius: CGFloat,
        masksToBounds: Bool,
        shadowOpacity: Float,
        shadowRadius: CGFloat,
        shadowOffset: CGSize,
        shadowColor: UIColor?
    ) {
        removeShadow()

        self.layer.cornerRadius = cornerRadius
        self.layer.masksToBounds = masksToBounds

        self.layer.shadowOpacity = shadowOpacity
        self.layer.shadowRadius = shadowRadius
        self.layer.shadowOffset = shadowOffset
        self.layer.shadowColor = shadowColor?.cgColor

        self.layer.shouldRasterize = true
        self.layer.rasterizationScale = UIScreen.main.scale
    }
}



// MARK: Apply border to view.

extension UIView {
    func applyBorder(borderColor: UIColor, borderWidth: CGFloat) {
        self.layer.borderColor = borderColor.cgColor
        self.layer.borderWidth = borderWidth
    }
}



/// Add gradient.
///
extension UIView {
    enum Direction {
        case vertical
        case horizontal
    }
    
    func applyGradient(colors: [CGColor], direction: Direction = .horizontal) {
        let gradient: CAGradientLayer = CAGradientLayer()
        gradient.colors = colors
        if direction == .horizontal {
            gradient.locations = [0, 1]
            gradient.startPoint = CGPoint(x: 0.25, y: 0.5)
            gradient.endPoint = CGPoint(x: 0.75, y: 0.5)
        }
        gradient.cornerRadius = self.layer.cornerRadius
        gradient.frame = self.layer.frame
        self.clipsToBounds = true
        self.layer.insertSublayer(gradient, at: 0)
    }
}

/// Hiding all subviews recursively.
///
extension UIView {
    func hideAllSubviews() {
        for subview in subviews {
            subview.isHidden = true
            subview.hideAllSubviews()
        }
    }
}

/// Hiding all subviews recursively.
///
extension UIView {
    var isRTL: Bool {
        LocalizationManager.shared.getLanguageDirection() == .rightToLeft
    }
}

extension UIView: AppLanguagesKeysAccessible {
    var appLanguageDirection: LanguageDirection {
        return LocalizationManager.shared.getLanguageDirection()
    }
}
