//
//  UITextField+icon.swift
//  Dhaiban
//
//  Created by <PERSON> on 16/02/2024.
//

import UIKit


/// Add icon to text field.
///
extension UITextField {
    
    enum Direction {
        case Left
        case Right
    }
    
    // Add image to textfield.
    
    func withImage(direction: Direction, image: UIImage) {
        let mainView: UIView?
        let view: UIView?
        let imageView = UIImageView(image: image)
        let xPosition: CGFloat = LocalizationManager.shared.getLanguageDirection() == .rightToLeft ? 3 : -3
        
        switch direction {
        case .Left:
            mainView = UIView(frame: CGRect(x: 0, y: 0, width: 30, height: 38))
            view = UIView(frame: CGRect(x: 0, y: 0, width: 30, height: 38))
        case .Right:
            mainView = UIView(frame: CGRect(x: 0, y: 0, width: 30, height: imageView.frame.height))
            view = UIView(frame: CGRect(x: xPosition, y: 0, width: 30, height: imageView.frame.height))
        }
        
        
        guard let mainView = mainView, let view = view else { return }
        view.clipsToBounds = true
        mainView.addSubview(view)
        
        imageView.contentMode = .scaleAspectFit
        imageView.center = view.center
        view.addSubview(imageView)
                
        switch direction {
        case .Left:
            self.leftViewMode = .always
            self.leftView = mainView
        case .Right:
            self.rightViewMode = .always
            self.rightView = mainView
        }
    }
}

/// Other way to set left image in text field.
///
extension UITextField {
    func setRightImage(_ image: UIImage?, imageWidth: CGFloat, padding: CGFloat) {
        let imageView = UIImageView(image: image)
        imageView.frame = CGRect(x: padding, y: 0, width: imageWidth, height: frame.height)
        imageView.contentMode = .center
        
        let containerView = UIView(frame: CGRect(x: 0, y: 0, width: imageWidth + 2 * padding, height: frame.height))
        containerView.addSubview(imageView)
        rightView = containerView
        rightViewMode = .always
    }
}

