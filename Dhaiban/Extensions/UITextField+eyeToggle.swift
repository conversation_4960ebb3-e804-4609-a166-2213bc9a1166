//
//  UITextField+eyeToggle.swift
//  <PERSON><PERSON>ban
//
//  Created by <PERSON> on 26/01/2024.
//

import UIKit


// MARK: Toggle password

extension UITextField {

    @available(iOS 13.0, *)
    public func enablePasswordToggle() {
        isSecureTextEntry = true
        let button = UIButton()
        setPasswordToggleImage(button)
        button.tintColor = .brownGrey
        button.imageView?.tintColor = .brownGrey
        
        if #available(iOS 15, *) {
            var buttonConfig = UIButton.Configuration.bordered()
            buttonConfig.baseBackgroundColor = .clear
            button.configuration = buttonConfig
        } else {
            button.imageEdgeInsets = Metrics.imageEdgeInsets
            button.frame = Metrics.buttonFrame
        }
            
        button.addTarget(self, action: #selector(self.togglePasswordView), for: .touchUpInside)
        self.rightView = button
        self.rightViewMode = .always
    }

    @available(iOS 13.0, *)
    private func setPasswordToggleImage(_ button: UIButton) {
        if(isSecureTextEntry) {
            button.setImage(UIImage.iconPasswordToggle, for: .normal)
        }else {
            button.setImage(UIImage.iconPasswordToggle, for: .normal)
        }
    }

    @available(iOS 13.0, *)
    @objc private func togglePasswordView(_ sender: Any) {
        self.isSecureTextEntry = !self.isSecureTextEntry
        setPasswordToggleImage(sender as! UIButton)
    }
}

// MARK: - Constants

private extension UITextField {
    enum Metrics {
        static let imageEdgeInsets = UIEdgeInsets(top: 0, left: -24, bottom: 0, right: 16)
        static let buttonFrame = CGRect(x: 4, y: 4, width: 20, height: 20)
    }
}

