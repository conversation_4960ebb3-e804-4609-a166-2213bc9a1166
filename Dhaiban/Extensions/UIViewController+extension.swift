//
//  UIViewController+Extension.swift
//  D<PERSON>ban
//
//  Created by <PERSON> on 15/05/2024.
//

import UIKit

extension UIViewController {
    func presentErrorAlert(errorType: AppErrorType) {
        let viewController = ErrorAlertViewController(errorType: errorType)
        viewController.modalPresentationStyle = .overFullScreen
        viewController.modalTransitionStyle = .crossDissolve
        self.present(viewController, animated: true)
    }
}

extension UIViewController {
    func hideNavigationBar(animated: Bool = false) {
        self.navigationController?.setNavigationBarHidden(true, animated: animated)
    }

    func showNavigationBar(animated: Bool = false) {
        self.navigationController?.setNavigationBarHidden(false, animated: animated)
    }
}

extension UIViewController {
    var appLanguageKeys: AppLanguagesKeysModel? {
        return ServiceLocator.keyValueWrapper.fetchAppLanguageKeys()
    }
}

extension UIViewController {
    func WhatsApp(phone:String){
        
        let appURL = URL(string: "https://api.whatsapp.com/send?phone=\(phone)")!
        print(appURL)
             if UIApplication.shared.canOpenURL(appURL) {
                 if #available(iOS 10.0, *) {
                     UIApplication.shared.open(appURL, options: [:], completionHandler: nil)
                 }
                 else {
                     UIApplication.shared.openURL(appURL)
                 }
             } else {
                 // WhatsApp is not installed
             }
        
    }
}

extension UINavigationController: UIGestureRecognizerDelegate {

    override open func viewDidLoad() {
        super.viewDidLoad()
        navigationController?.interactivePopGestureRecognizer?.delegate = self
        interactivePopGestureRecognizer?.delegate = nil
//        navigationController?.interactivePopGestureRecognizer?.isEnabled = true
    }

    public func gestureRecognizerShouldBegin(_ gestureRecognizer: UIGestureRecognizer) -> Bool {
        navigationController?.interactivePopGestureRecognizer?.isEnabled = true
        return viewControllers.count > 1
    }
}
