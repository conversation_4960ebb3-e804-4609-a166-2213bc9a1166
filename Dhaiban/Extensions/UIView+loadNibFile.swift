//
//  UIView+loadNibFile.swift
//  D<PERSON>ban
//
//  Created by <PERSON> on 21/01/2024.
//

import UIKit


extension UIView {
    @objc open var nibName: String { .init(describing: Self.self) }
    @objc open var bundle: Bundle { .init(for: Self.self) }

    /// Loads a view from a nib file and adds it as a subview to the current view instance..
    func loadViewFromNib() {
        let nib = UINib(nibName: nibName, bundle: bundle)
        
        guard let contentView = nib.instantiate(withOwner: self).first as? UIView else {
            assertionFailure("unable to find the content view")
            return
        }
        
        contentView.frame = bounds
        contentView.autoresizingMask = [.flexibleHeight, .flexibleWidth]
        addSubview(contentView)
    }
}
