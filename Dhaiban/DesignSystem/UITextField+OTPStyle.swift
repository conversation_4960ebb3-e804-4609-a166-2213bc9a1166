//
//  UITextField+OTPStyle.swift
//  D<PERSON>ban
//
//  Created by <PERSON> on 28/01/2024.
//

import UIKit



// MARK: Apply OTP text field

extension UITextField {
    public func applyOTPTextField() {
        self.translatesAutoresizingMaskIntoConstraints = false
        self.heightAnchor.constraint(equalToConstant: 50).isActive = true
        self.layer.cornerRadius = 6
        self.backgroundColor = .beige
        
        self.keyboardType = .phonePad
        self.textAlignment = .center
        self.textColor = .black
        self.font = .regular23
        
        self.addTarget(self, action: #selector(textFieldEditingDidBegin), for: .editingDidBegin)
        self.addTarget(self, action: #selector(textFieldEditingDidEnd), for: .editingDidEnd)
    }
    
    @objc private func textFieldEditingDidBegin() {
        self.layer.borderColor = UIColor.mediumBrown.cgColor
        self.layer.borderWidth = 2
    }
    @objc private func textFieldEditingDidEnd() {
        self.layer.borderColor = UIColor.clear.cgColor
        self.layer.borderWidth = 0
    }
}
