//
//  UIButton+Style.swift
//  Dhaiban
//
//  Created by <PERSON> on 21/01/2024.
//

import UIKit


// MARK: Button style

extension UIButton {
    enum ButtonStyle {
        case primary
        case normal
        case onboarding
        case onboarding1
        case onboarding2
        case onboarding3
        case onboarding4
        case skip
        case skip2
        case withBorder
        case style7
        case style19
        case style21
        case confirm
        case clear
        case secondary
        case delete
        case primaryWithoutBackground
        case beige2Style29
        case plus
        case style55
        case dismiss
        case explore
        case cash

        var textColor: UIColor {
            switch self {
            case .primary, .onboarding, .onboarding1, .onboarding2, .onboarding3, .onboarding4: return .white
            case .normal: return .mainBrown
            case .skip: return .greyishBrown
            case .skip2, .withBorder: return .veryLightPink
            case .style7: return .black100
            case .style19: return .mediumBrown
            case .style21: return .mainBrown
            case .confirm: return .white
            case .clear: return .black
            case .secondary: return .white
            case .delete: return .rustyRed
            case .primaryWithoutBackground: return .mediumBrown
            case .beige2Style29: return .black
            case .plus: return .mediumBrown
            case .style55: return .mediumBrown
            case .dismiss: return .mediumBrown
            case .explore: return .black
            case .cash: return .black
            }
        }
        
        var backgroundColor: UIColor {
            switch self {
            case .primary: return .mediumBrown
            case .normal, .skip, .skip2, .style7: return .clear
            case .onboarding: return .mainBrown
            case .onboarding1: return .darkBeige
            case .onboarding2: return .mediumBrown
            case .onboarding3: return .mudBrown
            case .onboarding4: return .chocolate
            case .withBorder: return .black34
            case .style19: return .clear
            case .style21: return .clear
            case .confirm: return .mediumBrown
            case .clear: return .clear
            case .secondary: return .black
            case .delete: return .clear
            case .primaryWithoutBackground: return .clear
            case .beige2Style29: return .beige2
            case .plus: return .beige
            case .style55: return .clear
            case .dismiss: return .clear
            case .explore: return .white
            case .cash: return .clear
            }
        }
        
        var buttonFont: UIFont {
            switch self {
            case .primary, .withBorder: return .semibold18
            case .normal: return .medium17
            case .onboarding: return .medium25
            case .onboarding1, .onboarding2, .onboarding3, .onboarding4: return .medium22
            case .skip: return .medium20
            case .skip2: return .regular16
            case .style7: return .regular13
            case .style19: return .regular12
            case .style21: return .regular12
            case .confirm: return .medium18
            case .clear: return .medium18
            case .secondary: return .medium18
            case .delete: return .medium18
            case .primaryWithoutBackground: return .semibold18
            case .beige2Style29: return .medium16
            case .plus: return .medium14
            case .style55: return .medium12
            case .dismiss: return .medium16
            case .explore: return .medium12
            case .cash: return .medium20
            }
        }
        
        var cornerRadius: CGFloat {
            switch self {
            default:
                return Metrics.defaultCornerRadius
            }
        }
        
        var normalImage: UIImage? {
            switch self {
            case .onboarding1: return .iconCountry
            case .onboarding2: return .iconLanguage
            case .onboarding3: return .iconCurrency
            default: return nil
            }
        }
        
        var selectedImage: UIImage? {
            switch self {
            default: return nil
            }
        }
        
        var borderColor: UIColor? {
            switch self {
            case .withBorder: return .white
            case .clear: return .mediumBrown
            default: return nil
            }
        }
        
        var borderWidth: CGFloat? {
            switch self {
            case .withBorder: return 1
            case .clear: return 1
            default: return nil
            }
        }
    }
}


// MARK: Apply button style

extension UIButton {
    func applyStyle(_ style: ButtonStyle) {
        backgroundColor = style.backgroundColor
        tintColor = style.textColor
        setImage(style.normalImage, for: .normal)
        setImage(style.selectedImage, for: .selected)
        titleLabel?.font = style.buttonFont
        if ServiceLocator.keyValueWrapper.fetchLanguageModel()?.direction == .rightToLeft {
            paddingTextAndImage(spacing: Metrics.defaultArabicPadding)
        } else {
            paddingTextAndImage(spacing: Metrics.defaultEnglishPadding)
        }
        
        layer.cornerRadius = style.cornerRadius
        layer.borderColor = style.borderColor?.cgColor
        layer.borderWidth = style.borderWidth ?? 0
    }
}


// MARK: Constants

private extension UIButton {
    enum Metrics {
        static let defaultBorderWidth: CGFloat = 1.8
        static let defaultCornerRadius: CGFloat = 15.0
        static let defaultEnglishPadding: CGFloat = -8.0
        static let defaultArabicPadding: CGFloat = 8.0
    }
}
