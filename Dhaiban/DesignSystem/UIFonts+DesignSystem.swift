//
//  UIFonts+DesignSystem.swift
//  Dhaiban
//
//  Created by <PERSON> on 21/01/2024.
//

import UIKit


// MARK: Dhaiban UIFont Helpers.

private extension UIFont {

    /// Returns a app font instance with the specified style.
    ///
    static func dhaibanFont(ofSize size: CGFloat, weight: UIFont.Weight) -> UIFont {
        
        let fontName: String = poppinsFontName(forWeight: weight)
        
        guard let font = UIFont(name: fontName, size: size) else {
            assertionFailure("Unable to get a font with name: \(fontName)")
            return UIFont.systemFont(ofSize: size, weight: weight)
        }
        return font
    }

    /// Returns a Poppins UIFont file name for the given weight.
    ///
    static func poppinsFontName(forWeight weight: UIFont.Weight) -> String {
        switch weight {
        case .bold:
            return "Poppins-Bold"
        case .semibold:
            return "Poppins-SemiBold"
        case .medium:
            return "Poppins-Medium"
        case .regular:
            return "Poppins-Regular"
        case .light:
            return "Poppins-Light"
        default:
            return "Poppins"
        }
    }
}



// MARK: Bold fonts

extension UIFont {

    static var bold44: UIFont {
        dhaibanFont(ofSize: 44, weight: .bold)
    }
    
    static var bold32: UIFont {
        dhaibanFont(ofSize: 32, weight: .bold)
    }
    
    static var bold28: UIFont {
        dhaibanFont(ofSize: 28, weight: .bold)
    }
    
    static var bold20: UIFont {
        dhaibanFont(ofSize: 20, weight: .bold)
    }
    
    static var bold18: UIFont {
        dhaibanFont(ofSize: 18, weight: .bold)
    }
    
    static var bold16: UIFont {
        dhaibanFont(ofSize: 16, weight: .bold)
    }
    
    static var bold15: UIFont {
        dhaibanFont(ofSize: 15, weight: .bold)
    }
    
    static var bold14: UIFont {
        dhaibanFont(ofSize: 14, weight: .bold)
    }
        
    static var bold13: UIFont {
        dhaibanFont(ofSize: 13, weight: .bold)
    }
    
    static var bold12: UIFont {
        dhaibanFont(ofSize: 12, weight: .bold)
    }
}


// MARK: SemiBold fonts

extension UIFont {

    static var semiBold44: UIFont {
        dhaibanFont(ofSize: 44, weight: .semibold)
    }
    
    static var semibold32: UIFont {
        dhaibanFont(ofSize: 32, weight: .semibold)
    }
    
    static var semibold28: UIFont {
        dhaibanFont(ofSize: 28, weight: .semibold)
    }
    
    static var semibold22: UIFont {
        dhaibanFont(ofSize: 22, weight: .semibold)
    }
    
    static var semibold20: UIFont {
        dhaibanFont(ofSize: 20, weight: .semibold)
    }
    
    static var semibold18: UIFont {
        dhaibanFont(ofSize: 18, weight: .semibold)
    }
    
    static var semibold16: UIFont {
        dhaibanFont(ofSize: 16, weight: .semibold)
    }
    
    static var semibold15: UIFont {
        dhaibanFont(ofSize: 15, weight: .semibold)
    }
    
    static var semibold14: UIFont {
        dhaibanFont(ofSize: 14, weight: .semibold)
    }
        
    static var semibold13: UIFont {
        dhaibanFont(ofSize: 13, weight: .semibold)
    }
    
    static var semibold12: UIFont {
        dhaibanFont(ofSize: 12, weight: .semibold)
    }
}


// MARK: Medium fonts

extension UIFont {

    static var medium44: UIFont {
        dhaibanFont(ofSize: 44, weight: .medium)
    }
    
    static var medium32: UIFont {
        dhaibanFont(ofSize: 32, weight: .medium)
    }
    
    static var medium28: UIFont {
        dhaibanFont(ofSize: 28, weight: .medium)
    }
    
    static var medium25: UIFont {
        dhaibanFont(ofSize: 25, weight: .medium)
    }
    static var medium24: UIFont {
        dhaibanFont(ofSize: 24, weight: .medium)
    }
    static var medium22: UIFont {
        dhaibanFont(ofSize: 22, weight: .medium)
    }
    
    static var medium20: UIFont {
        dhaibanFont(ofSize: 20, weight: .medium)
    }
    
    static var medium18: UIFont {
        dhaibanFont(ofSize: 18, weight: .medium)
    }
    
    static var medium17: UIFont {
        dhaibanFont(ofSize: 17, weight: .medium)
    }
    
    static var medium16: UIFont {
        dhaibanFont(ofSize: 16, weight: .medium)
    }
    
    static var medium15: UIFont {
        dhaibanFont(ofSize: 15, weight: .medium)
    }
    
    static var medium14: UIFont {
        dhaibanFont(ofSize: 14, weight: .medium)
    }
        
    static var medium13: UIFont {
        dhaibanFont(ofSize: 13, weight: .medium)
    }
    
    static var medium12: UIFont {
        dhaibanFont(ofSize: 12, weight: .medium)
    }
    static var medium10: UIFont {
        dhaibanFont(ofSize: 10, weight: .medium)
    }
}


// MARK: Regular fonts

extension UIFont {

    static var regular44: UIFont {
        dhaibanFont(ofSize: 44, weight: .regular)
    }
    
    static var regular32: UIFont {
        dhaibanFont(ofSize: 32, weight: .regular)
    }
    
    static var regular28: UIFont {
        dhaibanFont(ofSize: 28, weight: .regular)
    }
    
    static var regular23: UIFont {
        dhaibanFont(ofSize: 23, weight: .regular)
    }
    
    static var regular20: UIFont {
        dhaibanFont(ofSize: 20, weight: .regular)
    }
    
    static var regular17: UIFont {
        dhaibanFont(ofSize: 17, weight: .regular)
    }
    
    static var regular18: UIFont {
        dhaibanFont(ofSize: 18, weight: .regular)
    }
    
    static var regular16: UIFont {
        dhaibanFont(ofSize: 16, weight: .regular)
    }
    
    static var regular15: UIFont {
        dhaibanFont(ofSize: 15, weight: .regular)
    }
    
    static var regular14: UIFont {
        dhaibanFont(ofSize: 14, weight: .regular)
    }
        
    static var regular13: UIFont {
        dhaibanFont(ofSize: 13, weight: .regular)
    }
    
    static var regular12: UIFont {
        dhaibanFont(ofSize: 12, weight: .regular)
    }
    
    static var regular11: UIFont {
        dhaibanFont(ofSize: 11, weight: .regular)
    }
    
    static var regular10: UIFont {
        dhaibanFont(ofSize: 10, weight: .regular)
    }
    
    static var regular9: UIFont {
        dhaibanFont(ofSize: 9, weight: .regular)
    }
    
    static var regular8: UIFont {
        dhaibanFont(ofSize: 8, weight: .regular)
    }
}

// MARK: Light fonts

extension UIFont {
    static var light7: UIFont {
        dhaibanFont(ofSize: 7, weight: .light)
    }

    static var light10: UIFont {
        dhaibanFont(ofSize: 10, weight: .light)
    }

    static var light12: UIFont {
        dhaibanFont(ofSize: 12, weight: .light)
    }
    
    static var light14: UIFont {
        dhaibanFont(ofSize: 14, weight: .light)
    }
    
    static var light20: UIFont {
        dhaibanFont(ofSize: 20, weight: .light)
    }
}

