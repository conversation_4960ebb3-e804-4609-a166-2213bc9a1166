//
//  UILabel+Style.swift
//  Dhaiban
//
//  Created by <PERSON> on 21/01/2024.
//

import UIKit


// MARK: Label style

extension UILabel {
    enum LabelStyle {
        case textStyle
        case textStyle2
        case textStyle3
        case textStyle4
        case textStyle5
        case textStyle6
        case textStyle7
        case textStyle8
        case textStyle9
        case textStyle10
        case textStyle11
        case textStyle14
        case textStyle15
        case textStyle17
        case textStyle18
        case textStyle20
        case textStyle21
        case textStyle22
        case textStyle23
        case textStyle24
        case textStyle25
        case textStyle26
        case textStyle27
        case textStyle28
        case textStyle29
        case textStyle30
        case textStyle31
        case textStyle32
        case textStyle33
        case textStyle34
        case textStyle36
        case textStyle37
        case textStyle38
        case textStyle39
        case textStyle40
        case textStyle41
        case textStyle42
        case textStyle43
        case textStyle44
        case textStyle45
        case textStyle46
        case textStyle47
        case textStyle48
        case textSty<PERSON><PERSON>
        case textStyle51
        case textStyle52
        case textStyle53
        case textStyle54
        case textStyle56
        case textStyle57
        case textStyle59
        case textStyle60
        case textStyle61
        case textStyle62
        case textStyle63
        case textStyle64
        case textStyle65
        case textStyle66
        case textStyle67
        case textStyle69
        case textStyle72
        case textStyle73
        case textStyle74
        case textStyle75
        case textStyle76
        case textStyle81
        case textStyle82

        var textColor: UIColor {
            switch self {
            case .textStyle: return .white
            case .textStyle2: return .greyishBrown
            case .textStyle3: return .black
            case .textStyle4: return .black100
            case .textStyle5: return .gray200
            case .textStyle6: return .mainBrown
            case .textStyle7: return .black100
            case .textStyle8: return .greyishBrown
            case .textStyle9: return .black100
            case .textStyle10: return .rustyRed
            case .textStyle11: return .black
            case .textStyle14: return .black
            case .textStyle15: return .black
            case .textStyle17: return .black200
            case .textStyle18: return .rustyRed
            case .textStyle20: return .black
            case .textStyle21: return .greyish
            case .textStyle22: return .black
            case .textStyle23: return .black
            case .textStyle24: return .mainBrown
            case .textStyle25: return .black
            case .textStyle26: return .mainBrown
            case .textStyle27: return .veryLightPink
            case .textStyle28: return .mudBrown
            case .textStyle29: return .black
            case .textStyle30: return .brownishGrey
            case .textStyle31: return .white
            case .textStyle32: return .black100
            case .textStyle33: return .black100
            case .textStyle34: return .black
            case .textStyle36: return .black
            case .textStyle37: return .white
            case .textStyle38: return .black100
            case .textStyle39: return .greyish
            case .textStyle40: return .brownGrey2
            case .textStyle41: return .black100
            case .textStyle42: return .black100
            case .textStyle43: return .rustyRed
            case .textStyle44: return .greyishBrown
            case .textStyle45: return .mediumBrown
            case .textStyle46: return .greyishBrown
            case .textStyle47: return .white
            case .textStyle48: return .greyish
            case .textStyle50: return .black
            case .textStyle51: return .brownishGrey
            case .textStyle52: return .black
            case .textStyle53: return .black
            case .textStyle54: return .black
            case .textStyle56: return .greyish
            case .textStyle57: return .chocolate
            case .textStyle59: return .black
            case .textStyle60: return .brownishGrey
            case .textStyle61: return .brownishGrey
            case .textStyle62: return .mudBrown
            case .textStyle63: return .black
            case .textStyle64: return .white
            case .textStyle65: return .white
            case .textStyle66: return .black
            case .textStyle69: return .specGreen
            case .textStyle67: return .black
            case .textStyle72: return .systemYellow
            case .textStyle73: return .mediumBrown
            case .textStyle74: return .white
            case .textStyle75: return .greyishBrown
            case .textStyle76: return .white
            case .textStyle81: return .black
            case .textStyle82: return .black
            }
        }

        var labelFont: UIFont {
            switch self {
            case .textStyle: return .medium24
            case .textStyle2: return .regular18
            case .textStyle3: return .medium28
            case .textStyle4: return .semibold32
            case .textStyle5: return .regular17
            case .textStyle6: return .medium17
            case .textStyle7: return .regular15
            case .textStyle8: return .medium22
            case .textStyle9: return .regular16
            case .textStyle10: return .regular14
            case .textStyle11: return .medium24
            case .textStyle14: return .medium24
            case .textStyle15: return .medium20
            case .textStyle17: return .medium18
            case .textStyle18: return .regular16
            case .textStyle20: return .medium12
            case .textStyle21: return .regular11
            case .textStyle22: return .semibold18
            case .textStyle23: return .medium20
            case .textStyle24: return .medium12
            case .textStyle25: return .medium14
            case .textStyle26: return .medium14
            case .textStyle27: return .medium16
            case .textStyle28: return .regular18
            case .textStyle29: return .medium18
            case .textStyle30: return .medium16
            case .textStyle31: return .medium17
            case .textStyle32: return .medium14
            case .textStyle33: return .semibold14
            case .textStyle34: return .medium20
            case .textStyle36: return .medium16
            case .textStyle37: return .medium18
            case .textStyle38: return .medium16
            case .textStyle39: return .light14
            case .textStyle40: return .regular14
            case .textStyle41: return .semibold18
            case .textStyle42: return .regular14
            case .textStyle43: return .medium16
            case .textStyle44: return .medium16
            case .textStyle45: return .semibold18
            case .textStyle46: return .regular14
            case .textStyle47: return .medium16
            case .textStyle48: return .regular13
            case .textStyle50: return .medium18
            case .textStyle51: return .regular13
            case .textStyle52: return .medium16
            case .textStyle53: return .regular14
            case .textStyle54: return .semibold18
            case .textStyle56: return .regular14
            case .textStyle57: return .medium16
            case .textStyle59: return .semibold14
            case .textStyle60: return .regular10
            case .textStyle61: return .regular14
            case .textStyle62: return .semibold20
            case .textStyle63: return .medium14
            case .textStyle64: return .medium14
            case .textStyle65: return .regular12
            case .textStyle66: return .regular12
            case .textStyle69: return .medium16
            case .textStyle67: return .medium12
            case .textStyle72: return .regular12
            case .textStyle73: return .regular13
            case .textStyle74: return .medium16
            case .textStyle75: return .light7
            case .textStyle76: return .light7
            case .textStyle81: return .semibold22
            case .textStyle82: return .light20
            }
        }
    }
}


// MARK: Apply label style

extension UILabel {
    func applyStyle(_ style: LabelStyle) {
        textColor = style.textColor
        font = style.labelFont
    }
}

