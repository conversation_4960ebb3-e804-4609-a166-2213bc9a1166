//
//  UIImages+DesignSystem.swift
//  Dhaiban
//
//  Created by <PERSON> on 21/01/2024.
//

import UIKit


extension UIImage {
    static var tabbarUnselectedHome: UIImage {
        return UIImage(named: "icon-tabbar-unselected-home")!
    }
    
    static var tabbarSelectedHome: UIImage {
        return UIImage(named: "icon-tabbar-selected-home")!
    }
    
    static var tabbarUnselectedCategories: UIImage {
        return UIImage(named: "icon-tabbar-unselected-categories")!
    }
    
    static var tabbarSelectedCategories: UIImage {
        return UIImage(named: "icon-tabbar-selected-categories")!
    }
    
    static var tabbarUnselectedCart: UIImage {
        return UIImage(named: "icon-tabbar-unselected-cart")!
    }
    
    static var tabbarSelectedCart: UIImage {
        return UIImage(named: "icon-tabbar-selected-cart")!
    }
    
    static var tabbarUnselectedProfile: UIImage {
        return UIImage(named: "icon-tabbar-unselected-profile")!
    }
    
    static var tabbarSelectedProfile: UIImage {
        return UIImage(named: "icon-tabbar-selected-profile")!
    }

    static var notifications: UIImage {
        return UIImage(named: "icon-notifications")!
    }
    
    static var notificationNone: UIImage {
        return UIImage(named: "icon-notifications-none")!
    }
    
    static var notificationRefund: UIImage {
        return UIImage(named: "icon-notifications-refund")!
    }
    
    static var notificationOrderDelivery: UIImage {
        return UIImage(named: "icon-notifications-orderDelivery")!
    }

    static var wallet: UIImage {
        return UIImage(named: "icon-activity-wallet")!
    }
    
    static var refund: UIImage {
        return UIImage(named: "icon-activity-refund")!
    }
    
    static var orders: UIImage {
        return UIImage(named: "icon-activity-orders")!
    }
    
    static var favourites: UIImage {
        return UIImage(named: "icon-activity-favourites")!
    }
    
    static var placeholderProfile: UIImage {
        return UIImage(named: "icon-profile")!
    }
    
    static var addFill: UIImage {
        return UIImage(named: "icon-addFill")!
    }
    
    static var placeholderProductDhabian: UIImage {
        return UIImage(named: "icon-primary")!
    }
    
    static var arrowUp: UIImage {
        return UIImage(named: "icon-arrow-up")!
    }
    
    static var arrowDown: UIImage {
        return UIImage(named: "icon-arrow-down")!
    }
    
    static var customerServices: UIImage {
        return UIImage(named: "icon-customerServices")!
    }
    
    static var website: UIImage {
        return UIImage(named: "icon-website")!
    }
    
    static var facebook: UIImage {
        return UIImage(named: "icon-facebook")!
    }
    
    static var whatsApp: UIImage {
        return UIImage(named: "icon-whatsApp")!
    }
    
    static var twitter: UIImage {
        return UIImage(named: "icon-twitter")!
    }
    
    static var instagram: UIImage {
        return UIImage(named: "icon-instagram")!
    }
    
    static var emptyAddress: UIImage {
        return UIImage(named: "Location-Empty-State")!
    }
    
    static var emptySearch: UIImage {
        return UIImage(named: "Search-empty-state")!
    }
    
    static var emptyNotifiaton: UIImage {
        return UIImage(named: "Notification-empty-state")!
    }
    
    static var emptyFavourites: UIImage {
        return UIImage(named: "Fav-empty-state")!
    }
    
    static var emptyChat: UIImage {
        return UIImage(named: "chat-empty-state")!
    }
    
    static var emptyCart: UIImage {
        return UIImage(named: "Cart-empty-state")!
    }
    
    static var emptyImage: UIImage {
        return UIImage(named: "image-empty")!
    }
    
    static var checkboxChecked: UIImage {
        return UIImage(named: "icon-checkbox-checked")!
    }
    
    static var checkboxUnChecked: UIImage {
        return UIImage(named: "icon-checkbox-unchecked")!
    }
    
    static var fav: UIImage {
        return UIImage(named: "heart-fill")!
    }
    
    static var unFav: UIImage {
        return UIImage(named: "heart-empty")!
    }
    
    static var unWallet: UIImage {
        return UIImage(named: "wallet-Empty-State")!
    }
    
    static var visa: UIImage {
        return UIImage(named: "icon-visa")!
    }
    
    static var unreadNotification: UIImage {
        return UIImage(systemName: "bell.badge.fill")!
    }
    
    static var noUnreadNotification: UIImage {
        return UIImage(systemName: "bell.fill")!
    }
    
    static var momoPaymentPending: UIImage {
        return UIImage(named: "icon-payment-momopay-pending")!
    }

    static var momoPaymentConfirmed: UIImage {
        return UIImage(named: "icon-payment-momopay-confirmed")!
    }

    static var momoPaymentFailed: UIImage {
        return UIImage(named: "icon-payment-momopay-failed")!
    }
    
    static func image(hasUnreadNotification: Bool) -> UIImage {
        (
            hasUnreadNotification
            ? UIImage.unreadNotification
            : UIImage.noUnreadNotification
        )
        .withRenderingMode(.alwaysOriginal)
    }
}
