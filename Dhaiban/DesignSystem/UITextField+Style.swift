//
//  UITextField+Style.swift
//  Dhaiban
//
//  Created by <PERSON> on 21/01/2024.
//

import UIKit


// MARK: TextField style

extension UITextField {
    
    enum TextFieldStyle {
        case primary
        case search
        case normal
        case cardField
        case chatMessage

        var textColor: UIColor {
            switch self {
            case .primary, .normal: return .gray100
            case .search: return .greyish
            case .cardField: return .brownishGrey
            case .chatMessage: return .black
            }
        }
        
        var backgroundColor: UIColor {
            switch self {
            case .primary, .search: return .white
            case .normal, .chatMessage: return .clear
            case .cardField: return .paleWhite
            }
        }
        
        var textFieldFont: UIFont {
            switch self {
            case .primary, .search, .normal, .cardField, .chatMessage:
                return .regular14
            }
        }
        
        var cornerRadius: CGFloat {
            switch self {
            case .cardField:
                return 8
            case .chatMessage:
                return 0
            default:
                return Metrics.defaultCornerRadius
            }
        }
        
        var borderColor: UIColor? {
            switch self {
            case .primary, .search, .chatMessage: return .clear
            case .normal, .cardField: return .veryLightPink2
            }
        }
        
        var borderWidth: CGFloat? {
            switch self {
            case .primary, .search, .chatMessage:
                return nil
            case .normal, .cardField: return 1
            }
        }
    }
}


// MARK: Apply text field style

extension UITextField {
    func applyStyle(_ style: TextFieldStyle) {
        textColor = style.textColor
        backgroundColor = style.backgroundColor
        font = style.textFieldFont
        layer.cornerRadius = style.cornerRadius
        layer.borderWidth = style.borderWidth ?? 1.0
        layer.borderColor = style.borderColor?.cgColor
    }
}


// MARK: Constants

private extension UITextField {
    enum Metrics {
        static let defaultBorderWidth: CGFloat = 1
        static let defaultCornerRadius: CGFloat = 15.0
        static let defaultPadding: CGFloat = 4.0
    }
}

