//
//  SceneDelegate.swift
//  D<PERSON>ban
//
//  Created by <PERSON> on 21/01/2024.
//

import UIKit

class SceneDelegate: UIResponder, UIWindowSceneDelegate {

    // MARK: Properties
    
    var window: UIWindow?
    private(set) var appCoordinator: Coordinator!

    // MARK: Lifecycle

    func scene(
        _ scene: UIScene,
        willConnectTo session: UISceneSession,
        options connectionOptions: UIScene.ConnectionOptions
    ) {
        /// Init  app flow.
        ///
        guard let windowScene = (scene as? UIWindowScene) else { return }

        // Prepare window and app coordinator
        window = UIWindow(frame: UIScreen.main.bounds)
        window?.windowScene = windowScene
        
        let navigationController = UINavigationController()
        window?.rootViewController = navigationController
        window?.makeKeyAndVisible()

        appCoordinator = AppCoordinator(
            window: window!, 
            navigationController: navigationController
        )
        appCoordinator.start()

        LocalizationManager.shared.delegate = self

        UNUserNotificationCenter.current().delegate = self
        UNUserNotificationCenter.current().requestAuthorization(
            options: [.alert, .sound]
        ) { granted, error in
            if granted {
                print("Success in APNS registry")
            } else {
                if let error = error as? NSError {
                    print("Error in APNS registry: \(error.debugDescription)")
                } else {
                    print("Failed to obtain APNS permissions")
                }
            }
        }
    }

    func sceneDidDisconnect(_ scene: UIScene) {
        // Called as the scene is being released by the system.
        // This occurs shortly after the scene enters the background, or when its session is discarded.
        // Release any resources associated with this scene that can be re-created the next time the scene connects.
        // The scene may re-connect later, as its session was not necessarily discarded (see `application:didDiscardSceneSessions` instead).
    }

    func sceneDidBecomeActive(_ scene: UIScene) {
        // Called when the scene has moved from an inactive state to an active state.
        // Use this method to restart any tasks that were paused (or not yet started) when the scene was inactive.
    }

    func sceneWillResignActive(_ scene: UIScene) {
        // Called when the scene will move from an active state to an inactive state.
        // This may occur due to temporary interruptions (ex. an incoming phone call).
    }

    func sceneWillEnterForeground(_ scene: UIScene) {
        // Called as the scene transitions from the background to the foreground.
        // Use this method to undo the changes made on entering the background.
    }

    func sceneDidEnterBackground(_ scene: UIScene) {
        // Called as the scene transitions from the foreground to the background.
        // Use this method to save data, release shared resources, and store enough scene-specific state information
        // to restore the scene back to its current state.
    }
}

// MARK: - LocalizationManagerDelegate

extension SceneDelegate: LocalizationManagerDelegate {
    /// Start point when change language.
    ///
    func onLanguageDirectionUpdated() {
        self.appCoordinator.start()
    }
}


// MARK: - UNUserNotificationCenterDelegate

extension SceneDelegate: UNUserNotificationCenterDelegate {
    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        willPresent notification: UNNotification,
        withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void
    ) {
        // Handle the notification when the app is in the foreground
        // You can customize how the notification is presented here

        let userInfo = notification.request.content.userInfo
        print("Notification payload (foreground): \(userInfo)")

        guard
            let conversationId = userInfo["conversation_id"] as? Int,
            let dashboardCoordinator = appCoordinator.children.first(where: { $0 is DashboardCoordinator }),
            let topViewController = dashboardCoordinator.navigationController.topViewController
        else { return }

        /*
         - When conversations screen is open
           Reload conversations list
         - When a conversation screen is open
           - when conversation_id == notification.coversation_id
             reload last message
           - else
             show the push notification
         - else
           show the push notification
         */
        if let topViewController = topViewController as? ConversationsViewController {
            topViewController.viewModel.onPushNotificationMessageReceived()
        } else if let topViewController = topViewController as? ProductConversationViewController {
            if topViewController.viewModel.conversationId == conversationId {
                topViewController.viewModel.onPushNotificationMessageReceived()
            } else {
                completionHandler([.alert, .sound])
            }
        } else {
            completionHandler([.alert, .sound])
        }
    }

    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        didReceive response: UNNotificationResponse,
        withCompletionHandler completionHandler: @escaping () -> Void
    ) {
        // Handle user interaction with the notification
        // For example, you can navigate to a specific view controller based on the notification

        let userInfo = response.notification.request.content.userInfo

        /*
         - App in foreground
            - When a conversation screen is open
              reload last message
            - else
              navigate to conversation screen by conversation_id

         - App in background
            - open main tab controller at home tab
            - navigate to conversation screen by conversation_id
         */

        guard 
            let appCoordinator = appCoordinator,
            let conversationId = userInfo["conversation_id"] as? Int
        else { return }

        let dashboardCoordinator = appCoordinator.children.first(
            where: { $0 is DashboardCoordinator }
        )
        let topViewController = dashboardCoordinator?.navigationController.topViewController

        if let topViewController = topViewController as? ProductConversationViewController {
            topViewController.viewModel.onPushNotificationMessageTapped()
        } else {
            showProductConversationViewController(conversationId: conversationId)
        }

//        if let appCoordinator = appCoordinator {
//
//
//        } else { // App is in background
//            if let sceneDelegate = UIApplication.shared.connectedScenes.first?.delegate as? SceneDelegate,
//               let window = sceneDelegate.window {
//
//                let navigationController = UINavigationController()
//                window.rootViewController = navigationController
//                window.makeKeyAndVisible()
//
//                /// Confirm localization.
//                ///
//                LocalizationManager.shared.delegate = self
////                LocalizationManager.shared.setAppInnitLanguage()
//
//                // Listen for push notifications
//                appDelegate?.pushNotificationDelegate = self
//
//                appCoordinator = AppCoordinator(
//                    window: window,
//                    navigationController: navigationController
//                ) { [weak self] in
//                    self?.showProductConversationViewController(
//                        conversationId: 30
//                    )
//                }
//                appCoordinator.start()
//            }
//
//        }

        completionHandler()
    }

    private func showProductConversationViewController(conversationId: Int) {
        let dashboardCoordinator = appCoordinator.children.first(
            where: { $0 is DashboardCoordinator }
        )

        if let dashboardCoordinator = dashboardCoordinator as? DashboardCoordinator {
            dashboardCoordinator.showProductConversationViewController(
                conversationId: conversationId
            )
        }
    }
}
