//
//  AppDelegate.swift
//  D<PERSON>ban
//
//  Created by <PERSON> on 21/01/2024.
//

import UIKit
import IQ<PERSON>eyboardManagerSwift
import GoogleMaps
import Firebase
import FirebaseMessaging
import UserNotifications

@main
class AppDelegate: UIResponder, UIApplicationDelegate, MessagingDelegate, UNUserNotificationCenterDelegate {

    // MARK: Properties

    var window: UIWindow?

    // MARK: Lifecycle

    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
        application.registerForRemoteNotifications()
        setupFirebase()
        setupKeyboardManager()
        setupGoogleMaps()
        setupTabBarAppearance()

        return true
    }

    func application(_ application: UIApplication, configurationForConnecting connectingSceneSession: UISceneSession, options: UIScene.ConnectionOptions) -> UISceneConfiguration {
        return UISceneConfiguration(name: "Default Configuration", sessionRole: connectingSceneSession.role)
    }

    func application(_ application: UIApplication, didDiscardSceneSessions sceneSessions: Set<UISceneSession>) {
        // Called when the user discards a scene session.
        // If any sessions were discarded while the application was not running, this will be called shortly after application:didFinishLaunchingWithOptions.
        // Use this method to release any resources that were specific to the discarded scenes, as they will not return.
    }
    
    // Just enable portrait mode
    func application(_ application: UIApplication, supportedInterfaceOrientationsFor window: UIWindow?) -> UIInterfaceOrientationMask {
        return .portrait
    }
}

// MARK: - Startup Configurations

extension AppDelegate {
    private func setupFirebase() {
        FirebaseApp.configure()
        Messaging.messaging().delegate = self
    }

    private func setupKeyboardManager() {
        IQKeyboardManager.shared.isEnabled = true
    }

    private func setupGoogleMaps() {
        GMSServices.provideAPIKey("AIzaSyCbANqlnUIGMWePKS1LK901g_AkpBnh0-A")
    }

    private func setupTabBarAppearance() {
        if #available(iOS 13.0, *) {
            let appearance = UITabBarAppearance()
            appearance.configureWithDefaultBackground()
            appearance.backgroundImage = UIImage()
            appearance.shadowImage = UIImage()
            appearance.shadowColor = .clear
            appearance.backgroundColor = .white
            UITabBar.appearance().standardAppearance = appearance

            if #available(iOS 15.0, *) {
                UITabBar.appearance().scrollEdgeAppearance = appearance
            }

            setupTabBarColors(with: appearance.compactInlineLayoutAppearance)
            setupTabBarColors(with: appearance.inlineLayoutAppearance)
            setupTabBarColors(with: appearance.stackedLayoutAppearance)

        } else {
            UITabBar.appearance().backgroundImage = UIImage()
        }
    }

    private func setupTabBarColors(with appearance: UITabBarItemAppearance) {
        let tintColor = UIColor.mediumBrown
        appearance.normal.iconColor = tintColor
        appearance.normal.titleTextAttributes = [
            .foregroundColor : tintColor
        ]

        appearance.selected.iconColor = tintColor
        appearance.selected.titleTextAttributes = [
            .foregroundColor : tintColor
        ]
    }
}

// MARK: - MessagingDelegate

extension AppDelegate {
    func messaging(_ messaging: Messaging, didReceiveRegistrationToken fcmToken: String?) {
        if let fcmToken = fcmToken {
            print("FCM Token: \(fcmToken)")
            ServiceLocator.keyValueWrapper.saveDeviceToken(fcmToken)
            sendTokenToBackend(fcmToken)
        } else {
            print("Failed to receive FCM token")
        }
    }

    func sendTokenToBackend(_ token: String) {
        guard let urlString = "\(Constant.apiURL)/fcm-token".addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed),
              let url = URL(string: urlString) else {
            print("Invalid URL")
            return
        }

        var request = URLRequest(url: url)
        request.httpMethod = "PATCH"

        let body = "token=\(token)"
        request.httpBody = body.data(using: .utf8)

        request.setValue("application/x-www-form-urlencoded", forHTTPHeaderField: "Content-Type")

        // Add authorization header
        let headers = Header.shared.createHeader()
        headers.forEach { key, value in
            request.setValue(value, forHTTPHeaderField: key)
        }

        let task = URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                print("Error sending token to backend: \(error.localizedDescription)")
                return
            }

            guard let httpResponse = response as? HTTPURLResponse else {
                print("Unexpected response")
                return
            }

            if (200...299).contains(httpResponse.statusCode) {
                if let responseData = data {
                    if let decodedString = String(data: responseData, encoding: .utf8) {
                        print("success doing")
                        print(decodedString)
                    }
                }
            } else {
                print("Failed to send token. Status code: \(httpResponse.statusCode)")
            }
        }

        task.resume()
    }
}
